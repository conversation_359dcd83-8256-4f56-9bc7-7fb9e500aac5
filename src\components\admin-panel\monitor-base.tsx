"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Play, 
  Pause, 
  StopCircle, 
  RefreshCw, 
  Settings, 
  Monitor, 
  X,
  Filter,
  Delete
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";

// Define interfaces for our data structures
export interface Column {
  id: string;
  label: string;
  width: string;
}

export interface DataRow {
  id: number;
  name: string;
  status: string;
  server: string;
  queue: number;
  sent: number;
  delivered: number;
  bounced: number;
  dropped: number;
  [key: string]: string | number; // Index signature for dynamic access
}

export interface FiltersState {
  id?: string;
  [key: string]: string | undefined;
}

export interface MonitorProps {
  title: string;
  type: 'md' | 'mt' | 'sd' | 'st'; // md = MTA Drops, mt = MTA Tests, sd = SMTP Drops, st = SMTP Tests
  targetId: string; // The table ID for data attributes
  showBounceLogs?: boolean; // Only MTA monitors have bounce logs
  tableWidth?: string; // Table width percentage
  columns?: Column[]; // Optional columns override
  initialData?: DataRow[]; // Optional initial data
}

export default function MonitorBase({ 
  title, 
  type, 
  targetId, 
  showBounceLogs = false,
  tableWidth = '142%',
  columns: propColumns,
  initialData
}: MonitorProps) {
  const [data, setData] = useState<DataRow[]>([]);
  const [columns, setColumns] = useState<Column[]>([]);
  const [filters, setFilters] = useState<FiltersState>({});
  const [isAdmin, setIsAdmin] = useState(false);

  // Mock data loading - would be replaced with actual data fetching
  useEffect(() => {
    // Simulate admin check
    setIsAdmin(true);
    
    // Use provided columns or default ones
    const defaultColumns = [
      { id: 'name', label: 'Name', width: '10%' },
      { id: 'status', label: 'Status', width: '5%' },
      { id: 'server', label: 'Server', width: '10%' },
      { id: 'queue', label: 'Queue', width: '5%' },
      { id: 'sent', label: 'Sent', width: '8%' },
      { id: 'delivered', label: 'Delivered', width: '8%' },
      { id: 'bounced', label: 'Bounced', width: '8%' },
      { id: 'dropped', label: 'Dropped', width: '8%' }
    ];
    
    setColumns(propColumns || defaultColumns);
    
    // Use provided data or default
    const defaultData = [
      { id: 1, name: `${title} 1`, status: 'Active', server: 'Server A', queue: 123, sent: 5000, delivered: 4800, bounced: 150, dropped: 50 },
      { id: 2, name: `${title} 2`, status: 'Paused', server: 'Server B', queue: 0, sent: 3000, delivered: 2950, bounced: 40, dropped: 10 },
      { id: 3, name: `${title} 3`, status: 'Stopped', server: 'Server C', queue: 0, sent: 8000, delivered: 7600, bounced: 300, dropped: 100 }
    ];
    
    setData(initialData || defaultData);
  }, [propColumns, initialData, title]);

  const handleFilter = () => {
    console.log('Filtering with:', filters);
    // Would implement actual filtering logic here
  };

  const clearFilters = () => {
    setFilters({});
  };

  const handleProcessAction = (action: string, ids: number[]) => {
    console.log(`Executing ${action} for IDs:`, ids);
    // Would implement actual action logic here
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="p-4">
        <div className="portlet light portlet-fit portlet-datatable">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <span className="text-lg font-semibold">{title}</span>
            </div>
          </div>
          
          <div className="table-container">
            <div className="flex gap-2 mb-4">
              {showBounceLogs && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
                  title="Bounce Logs"
                >
                  <Settings className="h-4 w-4 mr-1" />
                  <span className="sr-only">Bounce Logs</span>
                </Button>
              )}
              
              <Button 
                variant="outline" 
                size="sm" 
                className="bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
                title="ReSend Process"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                <span className="sr-only">ReSend Process</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="bg-slate-100 text-slate-800 hover:bg-slate-200 border-slate-300"
                title="Show Process Details"
              >
                <Monitor className="h-4 w-4 mr-1" />
                <span className="sr-only">Show Process Details</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
                title="Resume Process"
                onClick={() => handleProcessAction('resume', [])}
              >
                <Play className="h-4 w-4 mr-1" />
                <span className="sr-only">Resume Process</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="bg-yellow-50 text-yellow-600 hover:bg-yellow-100 border-yellow-200"
                title="Pause Process"
                onClick={() => handleProcessAction('pause', [])}
              >
                <Pause className="h-4 w-4 mr-1" />
                <span className="sr-only">Pause Process</span>
              </Button>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
                title="Stop Process"
                onClick={() => handleProcessAction('stop', [])}
              >
                <StopCircle className="h-4 w-4 mr-1" />
                <span className="sr-only">Stop Process</span>
              </Button>
              
              {isAdmin && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
                  title="Delete Process"
                  onClick={() => handleProcessAction('delete', [])}
                >
                  <X className="h-4 w-4 mr-1" />
                  <span className="sr-only">Delete Process</span>
                </Button>
              )}
            </div>
            
            <div className="overflow-x-auto">
              <table 
                className="w-full border-collapse table-auto border border-gray-200 dark:border-gray-700"
                style={{ width: tableWidth }}
                id={targetId}
                data-type={type}
              >
                <thead>
                  <tr className="bg-gray-100 dark:bg-gray-800">
                    <th className="p-2 border border-gray-200 dark:border-gray-700 w-10">
                      <Checkbox id="select-all" />
                    </th>
                    <th className="p-2 border border-gray-200 dark:border-gray-700 w-16">ID</th>
                    
                    {columns.map(column => (
                      <th 
                        key={column.id} 
                        className="p-2 border border-gray-200 dark:border-gray-700"
                        style={{ width: column.width }}
                      >
                        {column.label}
                      </th>
                    ))}
                    
                    <th className="p-2 border border-gray-200 dark:border-gray-700 w-10"></th>
                  </tr>
                  
                  <tr className="bg-gray-50 dark:bg-gray-900">
                    <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={clearFilters}
                        className="h-6 w-6 p-0 text-red-500"
                      >
                        <Delete className="h-4 w-4" />
                      </Button>
                    </td>
                    <td className="p-2 border border-gray-200 dark:border-gray-700">
                      <Input 
                        type="text" 
                        className="h-7 text-xs" 
                        placeholder="ID"
                        value={filters.id || ''}
                        onChange={(e) => setFilters({...filters, id: e.target.value})}
                      />
                    </td>
                    
                    {columns.map(column => (
                      <td key={column.id} className="p-2 border border-gray-200 dark:border-gray-700">
                        <Input 
                          type="text" 
                          className="h-7 text-xs" 
                          placeholder={column.label}
                          value={filters[column.id] || ''}
                          onChange={(e) => setFilters({...filters, [column.id]: e.target.value})}
                        />
                      </td>
                    ))}
                    
                    <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={handleFilter}
                        className="h-6 w-6 p-0"
                      >
                        <Filter className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                </thead>
                
                <tbody>
                  {data.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50 dark:hover:bg-gray-900">
                      <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                        <Checkbox 
                          id={`row-${row.id}`} 
                          className="data-checkbox"
                        />
                      </td>
                      <td className="p-2 border border-gray-200 dark:border-gray-700">{row.id}</td>
                      
                      {columns.map(column => (
                        <td key={column.id} className="p-2 border border-gray-200 dark:border-gray-700">
                          {row[column.id]}
                        </td>
                      ))}
                      
                      <td className="p-2 border border-gray-200 dark:border-gray-700"></td>
                    </tr>
                  ))}
                </tbody>
                
                <tfoot>
                  <tr className="bg-gray-100 dark:bg-gray-800">
                    <td className="p-2 border border-gray-200 dark:border-gray-700" colSpan={2}>Total</td>
                    {columns.map((column, index) => (
                      <td key={index} className="p-2 border border-gray-200 dark:border-gray-700">
                        {column.id === 'sent' && data.reduce((sum, row) => sum + (row.sent || 0), 0)}
                        {column.id === 'delivered' && data.reduce((sum, row) => sum + (row.delivered || 0), 0)}
                        {column.id === 'bounced' && data.reduce((sum, row) => sum + (row.bounced || 0), 0)}
                        {column.id === 'dropped' && data.reduce((sum, row) => sum + (row.dropped || 0), 0)}
                        {!['sent', 'delivered', 'bounced', 'dropped'].includes(column.id) && ''}
                      </td>
                    ))}
                    <td className="p-2 border border-gray-200 dark:border-gray-700"></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 