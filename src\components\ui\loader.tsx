import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";

const loaderVariants = cva("animate-spin", {
  variants: {
    size: {
      default: "h-6 w-6",
      sm: "h-4 w-4",
      lg: "h-8 w-8",
      xl: "h-12 w-12"
    },
    variant: {
      default: "text-muted-foreground",
      primary: "text-primary",
      secondary: "text-secondary",
      destructive: "text-destructive",
      accent: "text-accent",
    },
    animation: {
      spin: "animate-spin",
      pulse: "animate-pulse",
      bounce: "animate-bounce",
    }
  },
  defaultVariants: {
    size: "default",
    variant: "default",
    animation: "spin",
  },
});

export interface LoaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    Omit<VariantProps<typeof loaderVariants>, "animation"> {
  label?: string;
  iconOnly?: boolean;
  progress?: number;
  animation?: "spin" | "pulse" | "bounce";
  showProgress?: boolean;
}

function Loader({
  className,
  size,
  variant,
  label = "Loading...",
  iconOnly = false,
  progress,
  animation = "spin",
  showProgress = false,
  ...props
}: LoaderProps) {
  const hasProgress = typeof progress === "number" && progress >= 0 && progress <= 100;
  
  return (
    <div
      role="status"
      className={cn("flex items-center gap-2", className)}
      {...props}
    >
      <div className="relative">
        <Loader2 
          className={cn(
            loaderVariants({ size, variant }),
            animation === "pulse" && "animate-pulse",
            animation === "bounce" && "animate-bounce",
            animation === "spin" && "animate-spin",
          )} 
        />
        
        {hasProgress && showProgress && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-[0.6rem] font-medium">
              {Math.round(progress)}%
            </span>
          </div>
        )}
      </div>
      
      {!iconOnly && !hasProgress && (
        <span className={cn("text-sm", 
          variant === "default" && "text-muted-foreground",
          variant === "primary" && "text-primary",
          variant === "secondary" && "text-secondary",
          variant === "destructive" && "text-destructive",
          variant === "accent" && "text-accent",
        )}>
          {label}
        </span>
      )}
      
      {!iconOnly && hasProgress && (
        <div className="flex flex-col gap-1">
          <span className={cn("text-sm", 
            variant === "default" && "text-muted-foreground",
            variant === "primary" && "text-primary",
            variant === "secondary" && "text-secondary",
            variant === "destructive" && "text-destructive",
            variant === "accent" && "text-accent",
          )}>
            {label}
          </span>
          {!showProgress && (
            <div className="h-1.5 w-24 bg-muted overflow-hidden rounded-full">
              <div 
                className={cn("h-full rounded-full transition-all duration-300",
                  variant === "default" && "bg-muted-foreground",
                  variant === "primary" && "bg-primary",
                  variant === "secondary" && "bg-secondary",
                  variant === "destructive" && "bg-destructive",
                  variant === "accent" && "bg-accent",
                )} 
                style={{ width: `${progress}%` }}
              />
            </div>
          )}
        </div>
      )}
      
      {iconOnly && <span className="sr-only">{label}</span>}
    </div>
  );
}

Loader.displayName = "Loader";

export { Loader, loaderVariants }; 