/**
 * Global Countries Utility
 * 
 * Provides a comprehensive list of countries with ISO 3166-1 alpha-2 codes
 * and full country names for consistent usage across the application.
 */

export interface Country {
  code: string;
  name: string;
}

/**
 * Comprehensive list of countries with ISO codes and full names
 * Sorted alphabetically by country name for better UX
 */
export const countries: Country[] = [
  { code: 'af', name: 'Afghanistan' },
  { code: 'al', name: 'Albania' },
  { code: 'dz', name: 'Algeria' },
  { code: 'as', name: 'American Samoa' },
  { code: 'ad', name: 'Andorra' },
  { code: 'ao', name: 'Angola' },
  { code: 'ai', name: 'Anguilla' },
  { code: 'ag', name: 'Antigua and Barbuda' },
  { code: 'ar', name: 'Argentina' },
  { code: 'am', name: 'Armenia' },
  { code: 'aw', name: 'Aruba' },
  { code: 'au', name: 'Australia' },
  { code: 'at', name: 'Austria' },
  { code: 'az', name: 'Azerbaijan' },
  { code: 'bs', name: 'Bahamas' },
  { code: 'bh', name: 'Bahrain' },
  { code: 'bd', name: 'Bangladesh' },
  { code: 'bb', name: 'Barbados' },
  { code: 'by', name: 'Belarus' },
  { code: 'be', name: 'Belgium' },
  { code: 'bz', name: 'Belize' },
  { code: 'bj', name: 'Benin' },
  { code: 'bm', name: 'Bermuda' },
  { code: 'bt', name: 'Bhutan' },
  { code: 'bo', name: 'Bolivia' },
  { code: 'ba', name: 'Bosnia and Herzegovina' },
  { code: 'bw', name: 'Botswana' },
  { code: 'bv', name: 'Bouvet Island' },
  { code: 'br', name: 'Brazil' },
  { code: 'io', name: 'British Indian Ocean Territory' },
  { code: 'vg', name: 'British Virgin Islands' },
  { code: 'bn', name: 'Brunei' },
  { code: 'bg', name: 'Bulgaria' },
  { code: 'bf', name: 'Burkina Faso' },
  { code: 'bi', name: 'Burundi' },
  { code: 'kh', name: 'Cambodia' },
  { code: 'cm', name: 'Cameroon' },
  { code: 'ca', name: 'Canada' },
  { code: 'cv', name: 'Cape Verde' },
  { code: 'bq', name: 'Caribbean Netherlands' },
  { code: 'ky', name: 'Cayman Islands' },
  { code: 'cf', name: 'Central African Republic' },
  { code: 'td', name: 'Chad' },
  { code: 'cl', name: 'Chile' },
  { code: 'cn', name: 'China' },
  { code: 'cx', name: 'Christmas Island' },
  { code: 'cc', name: 'Cocos (Keeling) Islands' },
  { code: 'co', name: 'Colombia' },
  { code: 'km', name: 'Comoros' },
  { code: 'ck', name: 'Cook Islands' },
  { code: 'cr', name: 'Costa Rica' },
  { code: 'hr', name: 'Croatia' },
  { code: 'cu', name: 'Cuba' },
  { code: 'cw', name: 'Curaçao' },
  { code: 'cy', name: 'Cyprus' },
  { code: 'cz', name: 'Czech Republic' },
  { code: 'cd', name: 'Democratic Republic of the Congo' },
  { code: 'dk', name: 'Denmark' },
  { code: 'dj', name: 'Djibouti' },
  { code: 'dm', name: 'Dominica' },
  { code: 'do', name: 'Dominican Republic' },
  { code: 'ec', name: 'Ecuador' },
  { code: 'eg', name: 'Egypt' },
  { code: 'sv', name: 'El Salvador' },
  { code: 'gq', name: 'Equatorial Guinea' },
  { code: 'er', name: 'Eritrea' },
  { code: 'ee', name: 'Estonia' },
  { code: 'sz', name: 'Eswatini' },
  { code: 'et', name: 'Ethiopia' },
  { code: 'fk', name: 'Falkland Islands' },
  { code: 'fo', name: 'Faroe Islands' },
  { code: 'fj', name: 'Fiji' },
  { code: 'fi', name: 'Finland' },
  { code: 'fr', name: 'France' },
  { code: 'gf', name: 'French Guiana' },
  { code: 'pf', name: 'French Polynesia' },
  { code: 'tf', name: 'French Southern Territories' },
  { code: 'ga', name: 'Gabon' },
  { code: 'gm', name: 'Gambia' },
  { code: 'ge', name: 'Georgia' },
  { code: 'de', name: 'Germany' },
  { code: 'gh', name: 'Ghana' },
  { code: 'gi', name: 'Gibraltar' },
  { code: 'gr', name: 'Greece' },
  { code: 'gl', name: 'Greenland' },
  { code: 'gd', name: 'Grenada' },
  { code: 'gp', name: 'Guadeloupe' },
  { code: 'gu', name: 'Guam' },
  { code: 'gt', name: 'Guatemala' },
  { code: 'gg', name: 'Guernsey' },
  { code: 'gn', name: 'Guinea' },
  { code: 'gw', name: 'Guinea-Bissau' },
  { code: 'gy', name: 'Guyana' },
  { code: 'ht', name: 'Haiti' },
  { code: 'hm', name: 'Heard Island and McDonald Islands' },
  { code: 'hn', name: 'Honduras' },
  { code: 'hk', name: 'Hong Kong' },
  { code: 'hu', name: 'Hungary' },
  { code: 'is', name: 'Iceland' },
  { code: 'in', name: 'India' },
  { code: 'id', name: 'Indonesia' },
  { code: 'ir', name: 'Iran' },
  { code: 'iq', name: 'Iraq' },
  { code: 'ie', name: 'Ireland' },
  { code: 'im', name: 'Isle of Man' },
  { code: 'il', name: 'Israel' },
  { code: 'it', name: 'Italy' },
  { code: 'ci', name: 'Ivory Coast' },
  { code: 'jm', name: 'Jamaica' },
  { code: 'jp', name: 'Japan' },
  { code: 'je', name: 'Jersey' },
  { code: 'jo', name: 'Jordan' },
  { code: 'kz', name: 'Kazakhstan' },
  { code: 'ke', name: 'Kenya' },
  { code: 'ki', name: 'Kiribati' },
  { code: 'kw', name: 'Kuwait' },
  { code: 'kg', name: 'Kyrgyzstan' },
  { code: 'la', name: 'Laos' },
  { code: 'lv', name: 'Latvia' },
  { code: 'lb', name: 'Lebanon' },
  { code: 'ls', name: 'Lesotho' },
  { code: 'lr', name: 'Liberia' },
  { code: 'ly', name: 'Libya' },
  { code: 'li', name: 'Liechtenstein' },
  { code: 'lt', name: 'Lithuania' },
  { code: 'lu', name: 'Luxembourg' },
  { code: 'mo', name: 'Macau' },
  { code: 'mg', name: 'Madagascar' },
  { code: 'mw', name: 'Malawi' },
  { code: 'my', name: 'Malaysia' },
  { code: 'mv', name: 'Maldives' },
  { code: 'ml', name: 'Mali' },
  { code: 'mt', name: 'Malta' },
  { code: 'mh', name: 'Marshall Islands' },
  { code: 'mq', name: 'Martinique' },
  { code: 'mr', name: 'Mauritania' },
  { code: 'mu', name: 'Mauritius' },
  { code: 'yt', name: 'Mayotte' },
  { code: 'mx', name: 'Mexico' },
  { code: 'fm', name: 'Micronesia' },
  { code: 'md', name: 'Moldova' },
  { code: 'mc', name: 'Monaco' },
  { code: 'mn', name: 'Mongolia' },
  { code: 'me', name: 'Montenegro' },
  { code: 'ms', name: 'Montserrat' },
  { code: 'ma', name: 'Morocco' },
  { code: 'mz', name: 'Mozambique' },
  { code: 'mm', name: 'Myanmar' },
  { code: 'na', name: 'Namibia' },
  { code: 'nr', name: 'Nauru' },
  { code: 'np', name: 'Nepal' },
  { code: 'nl', name: 'Netherlands' },
  { code: 'nc', name: 'New Caledonia' },
  { code: 'nz', name: 'New Zealand' },
  { code: 'ni', name: 'Nicaragua' },
  { code: 'ne', name: 'Niger' },
  { code: 'ng', name: 'Nigeria' },
  { code: 'nu', name: 'Niue' },
  { code: 'nf', name: 'Norfolk Island' },
  { code: 'kp', name: 'North Korea' },
  { code: 'mk', name: 'North Macedonia' },
  { code: 'mp', name: 'Northern Mariana Islands' },
  { code: 'no', name: 'Norway' },
  { code: 'om', name: 'Oman' },
  { code: 'pk', name: 'Pakistan' },
  { code: 'pw', name: 'Palau' },
  { code: 'ps', name: 'Palestine' },
  { code: 'pa', name: 'Panama' },
  { code: 'pg', name: 'Papua New Guinea' },
  { code: 'py', name: 'Paraguay' },
  { code: 'pe', name: 'Peru' },
  { code: 'ph', name: 'Philippines' },
  { code: 'pn', name: 'Pitcairn Islands' },
  { code: 'pl', name: 'Poland' },
  { code: 'pt', name: 'Portugal' },
  { code: 'pr', name: 'Puerto Rico' },
  { code: 'qa', name: 'Qatar' },
  { code: 'cg', name: 'Republic of the Congo' },
  { code: 're', name: 'Réunion' },
  { code: 'ro', name: 'Romania' },
  { code: 'ru', name: 'Russia' },
  { code: 'rw', name: 'Rwanda' },
  { code: 'bl', name: 'Saint Barthélemy' },
  { code: 'sh', name: 'Saint Helena' },
  { code: 'kn', name: 'Saint Kitts and Nevis' },
  { code: 'lc', name: 'Saint Lucia' },
  { code: 'mf', name: 'Saint Martin' },
  { code: 'pm', name: 'Saint Pierre and Miquelon' },
  { code: 'vc', name: 'Saint Vincent and the Grenadines' },
  { code: 'ws', name: 'Samoa' },
  { code: 'sm', name: 'San Marino' },
  { code: 'st', name: 'São Tomé and Príncipe' },
  { code: 'sa', name: 'Saudi Arabia' },
  { code: 'sn', name: 'Senegal' },
  { code: 'rs', name: 'Serbia' },
  { code: 'sc', name: 'Seychelles' },
  { code: 'sl', name: 'Sierra Leone' },
  { code: 'sg', name: 'Singapore' },
  { code: 'sx', name: 'Sint Maarten' },
  { code: 'sk', name: 'Slovakia' },
  { code: 'si', name: 'Slovenia' },
  { code: 'sb', name: 'Solomon Islands' },
  { code: 'so', name: 'Somalia' },
  { code: 'za', name: 'South Africa' },
  { code: 'gs', name: 'South Georgia and the South Sandwich Islands' },
  { code: 'kr', name: 'South Korea' },
  { code: 'ss', name: 'South Sudan' },
  { code: 'es', name: 'Spain' },
  { code: 'lk', name: 'Sri Lanka' },
  { code: 'sd', name: 'Sudan' },
  { code: 'sr', name: 'Suriname' },
  { code: 'sj', name: 'Svalbard and Jan Mayen' },
  { code: 'se', name: 'Sweden' },
  { code: 'ch', name: 'Switzerland' },
  { code: 'sy', name: 'Syria' },
  { code: 'tw', name: 'Taiwan' },
  { code: 'tj', name: 'Tajikistan' },
  { code: 'tz', name: 'Tanzania' },
  { code: 'th', name: 'Thailand' },
  { code: 'tl', name: 'Timor-Leste' },
  { code: 'tg', name: 'Togo' },
  { code: 'tk', name: 'Tokelau' },
  { code: 'to', name: 'Tonga' },
  { code: 'tt', name: 'Trinidad and Tobago' },
  { code: 'tn', name: 'Tunisia' },
  { code: 'tr', name: 'Turkey' },
  { code: 'tm', name: 'Turkmenistan' },
  { code: 'tc', name: 'Turks and Caicos Islands' },
  { code: 'tv', name: 'Tuvalu' },
  { code: 'ug', name: 'Uganda' },
  { code: 'ua', name: 'Ukraine' },
  { code: 'ae', name: 'United Arab Emirates' },
  { code: 'gb', name: 'United Kingdom' },
  { code: 'us', name: 'United States' },
  { code: 'um', name: 'United States Minor Outlying Islands' },
  { code: 'uy', name: 'Uruguay' },
  { code: 'vi', name: 'U.S. Virgin Islands' },
  { code: 'uz', name: 'Uzbekistan' },
  { code: 'vu', name: 'Vanuatu' },
  { code: 'va', name: 'Vatican City' },
  { code: 've', name: 'Venezuela' },
  { code: 'vn', name: 'Vietnam' },
  { code: 'wf', name: 'Wallis and Futuna' },
  { code: 'eh', name: 'Western Sahara' },
  { code: 'ye', name: 'Yemen' },
  { code: 'zm', name: 'Zambia' },
  { code: 'zw', name: 'Zimbabwe' }
];

/**
 * Get country name by ISO code
 * @param code - ISO 3166-1 alpha-2 country code (case insensitive)
 * @returns Full country name or the code in uppercase if not found
 */
export const getCountryName = (code: string): string => {
  const country = countries.find(c => c.code === code.toLowerCase());
  return country ? country.name : code.toUpperCase();
};

/**
 * Get country code by name (case insensitive partial match)
 * @param name - Country name or partial name
 * @returns ISO 3166-1 alpha-2 country code or null if not found
 */
export const getCountryCode = (name: string): string | null => {
  const country = countries.find(c =>
    c.name.toLowerCase().includes(name.toLowerCase())
  );
  return country ? country.code : null;
};

/**
 * Get country object by code
 * @param code - ISO 3166-1 alpha-2 country code (case insensitive)
 * @returns Country object or null if not found
 */
export const getCountryByCode = (code: string): Country | null => {
  return countries.find(c => c.code === code.toLowerCase()) || null;
};

/**
 * Get country object by name (case insensitive partial match)
 * @param name - Country name or partial name
 * @returns Country object or null if not found
 */
export const getCountryByName = (name: string): Country | null => {
  return countries.find(c =>
    c.name.toLowerCase().includes(name.toLowerCase())
  ) || null;
};

/**
 * Search countries by name (case insensitive)
 * @param query - Search query
 * @returns Array of matching countries
 */
export const searchCountries = (query: string): Country[] => {
  if (!query.trim()) return countries;

  const lowerQuery = query.toLowerCase();
  return countries.filter(country =>
    country.name.toLowerCase().includes(lowerQuery) ||
    country.code.toLowerCase().includes(lowerQuery)
  );
};

/**
 * Get all country codes
 * @returns Array of all ISO 3166-1 alpha-2 country codes
 */
export const getAllCountryCodes = (): string[] => {
  return countries.map(country => country.code);
};

/**
 * Get all country names
 * @returns Array of all country names
 */
export const getAllCountryNames = (): string[] => {
  return countries.map(country => country.name);
};

/**
 * Check if a country code is valid
 * @param code - ISO 3166-1 alpha-2 country code
 * @returns True if the code exists in the list
 */
export const isValidCountryCode = (code: string): boolean => {
  return countries.some(c => c.code === code.toLowerCase());
};

/**
 * Get countries sorted by name
 * @returns Array of countries sorted alphabetically by name
 */
export const getCountriesSortedByName = (): Country[] => {
  return [...countries].sort((a, b) => a.name.localeCompare(b.name));
};

/**
 * Get countries sorted by code
 * @returns Array of countries sorted alphabetically by code
 */
export const getCountriesSortedByCode = (): Country[] => {
  return [...countries].sort((a, b) => a.code.localeCompare(b.code));
};
