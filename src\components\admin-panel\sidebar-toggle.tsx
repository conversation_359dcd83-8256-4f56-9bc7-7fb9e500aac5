"use client";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ChevronLeft, MenuIcon } from "lucide-react";

interface SidebarToggleProps {
  isOpen: boolean;
  setIsOpen: () => void;
}

export function SidebarToggle({ isOpen, setIsOpen }: SidebarToggleProps) {
  return (
    <Button
      size="icon"
      variant="outline"
      className="absolute rounded-full bottom-[10px] right-0 translate-x-[50%] shadow-lg transition-all duration-300 ease-in-out z-50"
      onClick={setIsOpen}
    >
      {isOpen ? (
        <ChevronLeft className="h-4 w-4 transition-transform duration-300" />
      ) : (
        <MenuIcon className="h-4 w-4 transition-transform duration-300" />
      )}
    </Button>
  );
}