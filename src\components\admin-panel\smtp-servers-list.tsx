"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Play, 
  Pause, 
  StopCircle, 
  RefreshCw, 
  Settings, 
  Monitor, 
  X,
  Filter,
  Delete,
  Edit,
  PlusCircle,
  Mail
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// Define interfaces for our data structures
interface ServerColumn {
  id: string;
  label: string;
  width: string;
}

interface SmtpServerRow {
  id: number;
  name: string;
  host: string;
  port: number;
  status: string;
  authType: string;
  username: string;
  emailLimit: number;
  lastUsed: string;
  [key: string]: string | number; // Index signature for dynamic access
}

interface FiltersState {
  id?: string;
  [key: string]: string | undefined;
}

export default function SmtpServersList() {
  const [servers, setServers] = useState<SmtpServerRow[]>([]);
  const [columns, setColumns] = useState<ServerColumn[]>([]);
  const [filters, setFilters] = useState<FiltersState>({});
  const [isAdmin, setIsAdmin] = useState(false);

  // Mock data loading - would be replaced with actual data fetching
  useEffect(() => {
    // Simulate admin check
    setIsAdmin(true);
    
    // Set up columns
    setColumns([
      { id: 'name', label: 'Server Name', width: '15%' },
      { id: 'host', label: 'Host', width: '15%' },
      { id: 'port', label: 'Port', width: '5%' },
      { id: 'status', label: 'Status', width: '10%' },
      { id: 'authType', label: 'Auth Type', width: '10%' },
      { id: 'username', label: 'Username', width: '15%' },
      { id: 'emailLimit', label: 'Email Limit', width: '10%' },
      { id: 'lastUsed', label: 'Last Used', width: '15%' }
    ]);
    
    // Simulate server data
    setServers([
      { id: 1, name: 'SMTP-Server-01', host: 'smtp.server1.com', port: 587, status: 'Active', authType: 'PLAIN', username: '<EMAIL>', emailLimit: 5000, lastUsed: '2023-03-20 14:30:45' },
      { id: 2, name: 'SMTP-Server-02', host: 'smtp.server2.com', port: 465, status: 'Inactive', authType: 'LOGIN', username: '<EMAIL>', emailLimit: 10000, lastUsed: '2023-03-18 09:15:30' },
      { id: 3, name: 'SMTP-Server-03', host: 'smtp.server3.com', port: 587, status: 'Active', authType: 'OAUTH2', username: '<EMAIL>', emailLimit: 3000, lastUsed: '2023-03-21 11:45:10' }
    ]);
  }, []);

  const handleFilter = () => {
    console.log('Filtering with:', filters);
    // Would implement actual filtering logic here
  };

  const clearFilters = () => {
    setFilters({});
  };

  const handleServerAction = (action: string, ids: number[]) => {
    console.log(`Executing ${action} for SMTP server IDs:`, ids);
    // Would implement actual action logic here
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="p-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <span className="text-lg font-semibold">SMTP Servers</span>
          </div>
          <div className="flex gap-2">
            <Link href="/servers-manager/smtp/add">
              <Button 
                variant="outline"

                size="sm" 
                className="bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
              >
                <PlusCircle className="h-4 w-4 mr-1" />
                Add SMTP Server
              </Button>
            </Link>
          </div>
        </div>
        
        <div className="table-container">
          <div className="flex gap-2 mb-4">
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-slate-100 text-slate-800 hover:bg-slate-200 border-slate-300"
              title="Server Details"
            >
              <Monitor className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Server Details</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-cyan-50 text-cyan-600 hover:bg-cyan-100 border-cyan-200"
              title="Test Connection"
            >
              <Mail className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Test Connection</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
              title="Enable Server"
              onClick={() => handleServerAction('enable', [])}
            >
              <Play className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Enable</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
              title="Disable Server"
              onClick={() => handleServerAction('disable', [])}
            >
              <StopCircle className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Disable</span>
            </Button>
            
            {isAdmin && (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
                  title="Edit Server"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  <span className="sr-only md:not-sr-only md:inline-block">Edit</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
                  title="Delete Server"
                  onClick={() => handleServerAction('delete', [])}
                >
                  <X className="h-4 w-4 mr-1" />
                  <span className="sr-only md:not-sr-only md:inline-block">Delete</span>
                </Button>
              </>
            )}
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse table-auto border border-gray-200 dark:border-gray-700">
              <thead>
                <tr className="bg-gray-100 dark:bg-gray-800">
                  <th className="p-2 border border-gray-200 dark:border-gray-700 w-10">
                    <Checkbox id="select-all" />
                  </th>
                  <th className="p-2 border border-gray-200 dark:border-gray-700 w-16">ID</th>
                  
                  {columns.map(column => (
                    <th 
                      key={column.id} 
                      className="p-2 border border-gray-200 dark:border-gray-700"
                      style={{ width: column.width }}
                    >
                      {column.label}
                    </th>
                  ))}
                  
                  <th className="p-2 border border-gray-200 dark:border-gray-700 w-10"></th>
                </tr>
                
                <tr className="bg-gray-50 dark:bg-gray-900">
                  <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={clearFilters}
                      className="h-6 w-6 p-0 text-red-500"
                    >
                      <Delete className="h-4 w-4" />
                    </Button>
                  </td>
                  <td className="p-2 border border-gray-200 dark:border-gray-700">
                    <Input 
                      type="text" 
                      className="h-7 text-xs" 
                      placeholder="ID"
                      value={filters.id || ''}
                      onChange={(e) => setFilters({...filters, id: e.target.value})}
                    />
                  </td>
                  
                  {columns.map(column => (
                    <td key={column.id} className="p-2 border border-gray-200 dark:border-gray-700">
                      <Input 
                        type="text" 
                        className="h-7 text-xs" 
                        placeholder={column.label}
                        value={filters[column.id] || ''}
                        onChange={(e) => setFilters({...filters, [column.id]: e.target.value})}
                      />
                    </td>
                  ))}
                  
                  <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={handleFilter}
                      className="h-6 w-6 p-0"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              </thead>
              
              <tbody>
                {servers.map((server) => (
                  <tr key={server.id} className="hover:bg-gray-50 dark:hover:bg-gray-900">
                    <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                      <Checkbox 
                        id={`server-${server.id}`} 
                        className="data-checkbox"
                      />
                    </td>
                    <td className="p-2 border border-gray-200 dark:border-gray-700">{server.id}</td>
                    
                    {columns.map(column => (
                      <td key={column.id} className="p-2 border border-gray-200 dark:border-gray-700">
                        {server[column.id]}
                      </td>
                    ))}
                    
                    <td className="p-2 border border-gray-200 dark:border-gray-700"></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 