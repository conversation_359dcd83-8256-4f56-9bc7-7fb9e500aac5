"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Play, 
  Pause, 
  StopCircle, 
  RefreshCw, 
  Settings, 
  Monitor, 
  X,
  Filter,
  Delete,
  Edit,
  PlusCircle
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// Define interfaces for our data structures
interface ServerColumn {
  id: string;
  label: string;
  width: string;
}

interface ServerRow {
  id: number;
  name: string;
  ip: string;
  port: number;
  status: string;
  provider: string;
  location: string;
  maxConnections: number;
  connectedAt: string;
  [key: string]: string | number; // Index signature for dynamic access
}

interface FiltersState {
  id?: string;
  [key: string]: string | undefined;
}

export default function MtaServersList() {
  const [servers, setServers] = useState<ServerRow[]>([]);
  const [columns, setColumns] = useState<ServerColumn[]>([]);
  const [filters, setFilters] = useState<FiltersState>({});
  const [isAdmin, setIsAdmin] = useState(false);

  // Mock data loading - would be replaced with actual data fetching
  useEffect(() => {
    // Simulate admin check
    setIsAdmin(true);
    
    // Set up columns
    setColumns([
      { id: 'name', label: 'Server Name', width: '15%' },
      { id: 'ip', label: 'IP Address', width: '10%' },
      { id: 'port', label: 'Port', width: '5%' },
      { id: 'status', label: 'Status', width: '10%' },
      { id: 'provider', label: 'Provider', width: '12%' },
      { id: 'location', label: 'Location', width: '12%' },
      { id: 'maxConnections', label: 'Max Connections', width: '10%' },
      { id: 'connectedAt', label: 'Connected At', width: '12%' }
    ]);
    
    // Simulate server data
    setServers([
      { id: 1, name: 'MTA-Server-01', ip: '*************', port: 25, status: 'Active', provider: 'AWS', location: 'US East', maxConnections: 100, connectedAt: '2023-03-15 08:30:45' },
      { id: 2, name: 'MTA-Server-02', ip: '*************', port: 25, status: 'Inactive', provider: 'Google Cloud', location: 'Europe', maxConnections: 150, connectedAt: '2023-03-10 12:15:30' },
      { id: 3, name: 'MTA-Server-03', ip: '*************', port: 25, status: 'Maintenance', provider: 'Digital Ocean', location: 'Asia', maxConnections: 80, connectedAt: '2023-03-20 15:45:10' }
    ]);
  }, []);

  const handleFilter = () => {
    console.log('Filtering with:', filters);
    // Would implement actual filtering logic here
  };

  const clearFilters = () => {
    setFilters({});
  };

  const handleServerAction = (action: string, ids: number[]) => {
    console.log(`Executing ${action} for server IDs:`, ids);
    // Would implement actual action logic here
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="p-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <span className="text-lg font-semibold">MTA Servers</span>
          </div>
          <div className="flex gap-2">
            <Link href="/servers-manager/mta-servers/add">
              <Button 
                variant="outline" 
                size="sm" 
                className="bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
              >
                <PlusCircle className="h-4 w-4 mr-1" />
                Add Server
              </Button>
            </Link>
          </div>
        </div>
        
        <div className="table-container">
          <div className="flex gap-2 mb-4">
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-slate-100 text-slate-800 hover:bg-slate-200 border-slate-300"
              title="Server Details"
            >
              <Monitor className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Server Details</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
              title="Start Server"
              onClick={() => handleServerAction('start', [])}
            >
              <Play className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Start</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-yellow-50 text-yellow-600 hover:bg-yellow-100 border-yellow-200"
              title="Pause Server"
              onClick={() => handleServerAction('pause', [])}
            >
              <Pause className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Pause</span>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
              title="Stop Server"
              onClick={() => handleServerAction('stop', [])}
            >
              <StopCircle className="h-4 w-4 mr-1" />
              <span className="sr-only md:not-sr-only md:inline-block">Stop</span>
            </Button>
            
            {isAdmin && (
              <>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
                  title="Edit Server"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  <span className="sr-only md:not-sr-only md:inline-block">Edit</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
                  title="Delete Server"
                  onClick={() => handleServerAction('delete', [])}
                >
                  <X className="h-4 w-4 mr-1" />
                  <span className="sr-only md:not-sr-only md:inline-block">Delete</span>
                </Button>
              </>
            )}
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse table-auto border border-gray-200 dark:border-gray-700">
              <thead>
                <tr className="bg-gray-100 dark:bg-gray-800">
                  <th className="p-2 border border-gray-200 dark:border-gray-700 w-10">
                    <Checkbox id="select-all" />
                  </th>
                  <th className="p-2 border border-gray-200 dark:border-gray-700 w-16">ID</th>
                  
                  {columns.map(column => (
                    <th 
                      key={column.id} 
                      className="p-2 border border-gray-200 dark:border-gray-700"
                      style={{ width: column.width }}
                    >
                      {column.label}
                    </th>
                  ))}
                  
                  <th className="p-2 border border-gray-200 dark:border-gray-700 w-10"></th>
                </tr>
                
                <tr className="bg-gray-50 dark:bg-gray-900">
                  <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={clearFilters}
                      className="h-6 w-6 p-0 text-red-500"
                    >
                      <Delete className="h-4 w-4" />
                    </Button>
                  </td>
                  <td className="p-2 border border-gray-200 dark:border-gray-700">
                    <Input 
                      type="text" 
                      className="h-7 text-xs" 
                      placeholder="ID"
                      value={filters.id || ''}
                      onChange={(e) => setFilters({...filters, id: e.target.value})}
                    />
                  </td>
                  
                  {columns.map(column => (
                    <td key={column.id} className="p-2 border border-gray-200 dark:border-gray-700">
                      <Input 
                        type="text" 
                        className="h-7 text-xs" 
                        placeholder={column.label}
                        value={filters[column.id] || ''}
                        onChange={(e) => setFilters({...filters, [column.id]: e.target.value})}
                      />
                    </td>
                  ))}
                  
                  <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={handleFilter}
                      className="h-6 w-6 p-0"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              </thead>
              
              <tbody>
                {servers.map((server) => (
                  <tr key={server.id} className="hover:bg-gray-50 dark:hover:bg-gray-900">
                    <td className="p-2 border border-gray-200 dark:border-gray-700 text-center">
                      <Checkbox 
                        id={`server-${server.id}`} 
                        className="data-checkbox"
                      />
                    </td>
                    <td className="p-2 border border-gray-200 dark:border-gray-700">{server.id}</td>
                    
                    {columns.map(column => (
                      <td key={column.id} className="p-2 border border-gray-200 dark:border-gray-700">
                        {server[column.id]}
                      </td>
                    ))}
                    
                    <td className="p-2 border border-gray-200 dark:border-gray-700"></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 