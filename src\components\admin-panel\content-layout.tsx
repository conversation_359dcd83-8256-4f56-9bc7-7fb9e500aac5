"use client";

import { Navbar } from "@/components/admin-panel/navbar";
import { ClientOnly } from "@/components/client-only";
import { HelpButton } from "@/components/admin-panel/help-button";

interface ContentLayoutProps {
  title: string;
  children: React.ReactNode;
  headerActions?: React.ReactNode;
  showHelpButton?: boolean;
}

export function ContentLayout({ 
  title, 
  children, 
  headerActions, 
  showHelpButton = true 
}: ContentLayoutProps) {
  const content = (
    <div>
      <Navbar 
        title={title} 
        actions={
          <>
            {showHelpButton && <HelpButton />}
            {headerActions}
          </>
        } 
      />
      <div className="container pt-6 pb-6 px-4 sm:px-6 max-w-full overflow-x-hidden">
        <div className="relative" style={{ contain: 'layout paint', isolation: 'isolate' }}>
          {children}
        </div>
      </div>
    </div>
  );

  return <ClientOnly>{content}</ClientOnly>;
}
