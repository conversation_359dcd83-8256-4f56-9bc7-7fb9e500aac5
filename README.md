# SendFlux

SendFlux is a powerful emailing platform with advanced features and a clean, modern interface.

## Features

- Modern, responsive design
- Dark/Light theme support
- Comprehensive mail management
- Clean and intuitive interface
- Built with Next.js and TypeScript

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Tech Stack

- Next.js 15
- TypeScript
- Tailwind CSS
- Radix UI
- Lucide React Icons

## License

This project is licensed under the MIT License.