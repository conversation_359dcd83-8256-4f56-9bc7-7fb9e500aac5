import { cn } from "@/lib/utils";
import { Loader } from "@/components/ui/loader";
import { VariantProps } from "class-variance-authority";
import { loaderVariants } from "@/components/ui/loader";

interface PageLoaderProps {
  className?: string;
  fullScreen?: boolean;
  label?: string;
  variant?: VariantProps<typeof loaderVariants>["variant"];
  animation?: "spin" | "pulse" | "bounce";
  progress?: number;
  showProgressBar?: boolean;
  showProgressInSpinner?: boolean;
  blurBackground?: boolean;
}

export function PageLoader({
  className,
  fullScreen = false,
  label = "Loading page...",
  variant = "primary",
  animation = "spin",
  progress,
  showProgressBar = false,
  showProgressInSpinner = false,
  blurBackground = true,
}: PageLoaderProps) {
  const hasProgress = typeof progress === "number" && progress >= 0 && progress <= 100;

  return (
    <div 
      className={cn(
        "flex flex-col items-center justify-center", 
        fullScreen && "fixed inset-0 z-50",
        fullScreen && blurBackground && "bg-background/80 backdrop-blur-sm",
        fullScreen && !blurBackground && "bg-background/95",
        !fullScreen && "w-full h-full min-h-[400px]",
        className
      )}
    >
      <div className="flex flex-col items-center justify-center gap-4">
        <Loader 
          size="xl" 
          label={label}
          variant={variant}
          animation={animation}
          progress={progress}
          showProgress={showProgressInSpinner}
        />
        
        {hasProgress && showProgressBar && (
          <div className="w-64 mt-2">
            <div className="h-2 w-full bg-muted overflow-hidden rounded-full">
              <div 
                className={cn("h-full rounded-full transition-all duration-300",
                  variant === "default" && "bg-muted-foreground",
                  variant === "primary" && "bg-primary",
                  variant === "secondary" && "bg-secondary",
                  variant === "destructive" && "bg-destructive",
                  variant === "accent" && "bg-accent",
                )} 
                style={{ width: `${progress}%` }}
              />
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-muted-foreground">0%</span>
              <span className="text-xs text-muted-foreground">{Math.round(progress)}%</span>
              <span className="text-xs text-muted-foreground">100%</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 