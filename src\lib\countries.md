# Countries Utility

A comprehensive global countries utility providing ISO 3166-1 alpha-2 country codes and full country names for consistent usage across the application.

## Features

- Complete list of 249 countries with ISO codes and full names
- Case-insensitive search and lookup functions
- Utility functions for common operations
- TypeScript support with proper interfaces
- Optimized for performance and ease of use

## Usage Examples

### Basic Import

```typescript
import { 
  countries, 
  getCountryName, 
  getCountryCode, 
  searchCountries 
} from "@/lib/countries";
```

### Get Country Name by Code

```typescript
import { getCountryName } from "@/lib/countries";

const countryName = getCountryName('us'); // "United States"
const countryName2 = getCountryName('GB'); // "United Kingdom" (case insensitive)
const fallback = getCountryName('invalid'); // "INVALID" (fallback to uppercase code)
```

### Get Country Code by Name

```typescript
import { getCountryCode } from "@/lib/countries";

const code = getCountryCode('United States'); // "us"
const code2 = getCountryCode('united kingdom'); // "gb" (case insensitive)
const code3 = getCountryCode('Germany'); // "de"
```

### Search Countries

```typescript
import { searchCountries } from "@/lib/countries";

const results = searchCountries('United'); // Returns countries containing "United"
const results2 = searchCountries('us'); // Returns countries with "us" in name or code
```

### Get Country Objects

```typescript
import { getCountryByCode, getCountryByName } from "@/lib/countries";

const country = getCountryByCode('fr'); // { code: 'fr', name: 'France' }
const country2 = getCountryByName('Japan'); // { code: 'jp', name: 'Japan' }
```

### Validation

```typescript
import { isValidCountryCode } from "@/lib/countries";

const isValid = isValidCountryCode('us'); // true
const isInvalid = isValidCountryCode('invalid'); // false
```

### Get All Countries

```typescript
import { 
  countries, 
  getAllCountryCodes, 
  getAllCountryNames,
  getCountriesSortedByName 
} from "@/lib/countries";

const allCountries = countries; // Full array of country objects
const codes = getAllCountryCodes(); // Array of all country codes
const names = getAllCountryNames(); // Array of all country names
const sorted = getCountriesSortedByName(); // Countries sorted alphabetically by name
```

## React Component Examples

### Country Dropdown

```tsx
import { countries } from "@/lib/countries";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export function CountrySelect() {
  return (
    <Select>
      <SelectTrigger>
        <SelectValue placeholder="Select a country" />
      </SelectTrigger>
      <SelectContent>
        {countries.map((country) => (
          <SelectItem key={country.code} value={country.code}>
            {country.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
```

### Country Flag Component

```tsx
import { getCountryName } from "@/lib/countries";

interface CountryFlagProps {
  countryCode: string;
}

export function CountryFlag({ countryCode }: CountryFlagProps) {
  const countryName = getCountryName(countryCode);
  
  return (
    <div className="flex items-center justify-center">
      <img
        src={`https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`}
        className="h-4 w-6 object-cover"
        alt={countryName}
        title={countryName}
      />
    </div>
  );
}
```

### Searchable Country List

```tsx
import { useState } from "react";
import { searchCountries } from "@/lib/countries";
import { Input } from "@/components/ui/input";

export function CountrySearch() {
  const [query, setQuery] = useState("");
  const results = searchCountries(query);

  return (
    <div>
      <Input
        placeholder="Search countries..."
        value={query}
        onChange={(e) => setQuery(e.target.value)}
      />
      <ul>
        {results.map((country) => (
          <li key={country.code}>
            {country.name} ({country.code.toUpperCase()})
          </li>
        ))}
      </ul>
    </div>
  );
}
```

## API Reference

### Types

```typescript
interface Country {
  code: string; // ISO 3166-1 alpha-2 country code
  name: string; // Full country name
}
```

### Functions

- `getCountryName(code: string): string` - Get country name by code
- `getCountryCode(name: string): string | null` - Get country code by name
- `getCountryByCode(code: string): Country | null` - Get country object by code
- `getCountryByName(name: string): Country | null` - Get country object by name
- `searchCountries(query: string): Country[]` - Search countries by name or code
- `getAllCountryCodes(): string[]` - Get all country codes
- `getAllCountryNames(): string[]` - Get all country names
- `isValidCountryCode(code: string): boolean` - Check if country code is valid
- `getCountriesSortedByName(): Country[]` - Get countries sorted by name
- `getCountriesSortedByCode(): Country[]` - Get countries sorted by code

### Constants

- `countries: Country[]` - Complete array of all countries

## Migration from Local Arrays

If you have existing local country arrays, you can easily migrate:

**Before:**
```typescript
const countries = [
  { code: 'us', name: 'United States' },
  { code: 'gb', name: 'United Kingdom' },
  // ... more countries
];

const getCountryName = (code: string) => {
  const country = countries.find(c => c.code === code);
  return country ? country.name : code.toUpperCase();
};
```

**After:**
```typescript
import { getCountryName } from "@/lib/countries";
// Remove local countries array and getCountryName function
// Use imported getCountryName directly
```

This provides better consistency, more complete data, and additional utility functions.
