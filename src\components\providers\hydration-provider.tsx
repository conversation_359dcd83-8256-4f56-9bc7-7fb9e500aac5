"use client";

import React, { ReactNode, createContext, useEffect, useState, useContext } from 'react';

// Create a context to track hydration state
interface HydrationContextType {
  isHydrated: boolean;
}

const HydrationContext = createContext<HydrationContextType>({
  isHydrated: false,
});

// Hook to use the hydration context
export const useHydration = () => useContext(HydrationContext);

// Provider component
interface HydrationProviderProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function HydrationProvider({ children, fallback = null }: HydrationProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // This effect only runs on the client
    setIsHydrated(true);
  }, []);

  return (
    <HydrationContext.Provider value={{ isHydrated }}>
      {isHydrated ? children : fallback}
    </HydrationContext.Provider>
  );
} 