﻿"use client";

import Link from "next/link";
import { useState, useRef, useEffect, useCallback, useMemo } from "react";
// Import icons individually to avoid barrel optimization conflicts
import { 
  X, Plus, ChevronLeft, ChevronRight, RefreshCw, 
  Info, Link as LinkIcon, CheckSquare, Square, 
  Search, ChevronsLeft, ChevronsRight, ArrowUpRight,
  Link2, Maximize2, Minimize2, PlusCircle, ChevronUp, ChevronDown,
  Clock, CheckCircle, Mail, MousePointer, UserMinus, User, Globe, Check,
  LayoutGrid, Snowflake, XCircle, Star, Server, Download, Database, Loader2,
  ArrowUp, RotateCcw, Shuffle, ExternalLink, Monitor, Tablet, Smartphone
} from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog'
import * as DialogPrimitive from "@radix-ui/react-dialog"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { useUISettings } from "@/hooks/use-ui-settings";
import { useStore } from "@/hooks/use-store";
import { cn } from "@/lib/utils";
import { getCountryName, countries } from "@/lib/countries";

export default function ProductionSendPage() {
  // Add styles to the component - place this near the top of the component
  useEffect(() => {
    // Add a style tag to hide scrollbars but maintain scrolling functionality
    const style = document.createElement('style');
    style.textContent = `
      .overflow-y-auto {
        scrollbar-width: none;
        -ms-overflow-style: none;
      }
      
      .overflow-y-auto::-webkit-scrollbar {
        display: none;
        width: 0;
      }
      
      /* Increase dropdown max height to show all items */
      [role="listbox"], 
      .max-h-\\[200px\\] {
        max-height: 400px !important;
        padding-bottom: 8px !important;
      }
      
      /* Ensure dropdown items are fully visible */
      .relative.flex.w-full.cursor-pointer {
        padding-top: 6px !important;
        padding-bottom: 6px !important;
      }
      
      /* Fix dropdown positioning */
      [data-radix-popper-content-wrapper] {
        z-index: 50 !important;
      }
      
      /* Fix card overflow issues */
      .card-container {
        max-width: 100%;
        overflow-x: hidden !important;
        position: relative;
        z-index: 1;
        contain: layout;
      }
      
      /* Remove any overlay or gradient effects from textareas */
      textarea, 
      .flex-grow textarea, 
      .h-full textarea {
        position: relative;
        overflow: auto !important;
        max-height: none !important;
      }
      
      /* Ensure no elements have overlay effects in WideArea mode */
      [class*="WideArea"] .overflow-auto::before,
      [class*="WideArea"] .overflow-auto::after,
      [class*="WideArea"] textarea::before,
      [class*="WideArea"] textarea::after,
      [class*="WideArea"] .flex-grow::before,
      [class*="WideArea"] .flex-grow::after {
        display: none !important;
        content: none !important;
        background: none !important;
        background-image: none !important;
        mask-image: none !important;
        -webkit-mask-image: none !important;
      }
      
      /* Responsive card layout */
      @media (max-width: 1280px) {
        .card-container > div {
          flex-direction: column;
        }
        .card-container .w-1\/2 {
          width: 100%;
          margin-bottom: 1rem;
        }
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  
  const [isHeaderCardExpanded, setIsHeaderCardExpanded] = useState(true);
  const [isCreativeCardExpanded, setIsCreativeCardExpanded] = useState(true);
  const [isCreativeWideAreaMode, setIsCreativeWideAreaMode] = useState<boolean>(false);
  const [isHeaderWideAreaMode, setIsHeaderWideAreaMode] = useState<boolean>(false);
  const [isPreviewWideAreaMode, setIsPreviewWideAreaMode] = useState<boolean>(false);
  const [previewScale, setPreviewScale] = useState<number>(1);
  const [deviceView, setDeviceView] = useState<'pc' | 'phone' | 'tablet'>('pc');
  // Add state for controlling the Clean Data modal visibility
  const [isCleanDataModalOpen, setIsCleanDataModalOpen] = useState(false);
  const [isSuppressionFileLoading, setIsSuppressionFileLoading] = useState(false);
  const [isCleanDataLoading, setIsCleanDataLoading] = useState(false);
  const [suppressionFileReady, setSuppressionFileReady] = useState(false);
  
  // Get UI Settings
  const uiSettings = useStore(useUISettings, (state) => state);
  
  // Define proper interfaces for our data
  interface HeaderItem {
    id: number;
    name: string;
    template: string;
    content: string;
  }
  
  // Interface for creative items
  interface CreativeItem {
    id: number;
    name: string;
    content: string;
  }

  // Interface for server items
  interface ServerItem {
    id: number;
    name: string;
    isSelected: boolean;
  }
  
  // State for managing multiple headers
  const [headers, setHeaders] = useState<HeaderItem[]>([
    {
      id: 1,
      name: 'Header 1',
      template: 'default',
      content: `MIME-Version: 1.0
Message-Id: <[a_7].[n_5].[n_3].[a_3]@[domain]>
From: [a_5] <[a_7]@[domain]>
Subject: [ip] [server] [an_5]
Reply-To: reply_to@[domain]
To: [email]
Content-Transfer-Encoding: [content_transfer_encoding]
Content-Type: [content_type]; charset=[charset]
Date: [mail_date]`
    }
  ]);
  const [activeHeaderId, setActiveHeaderId] = useState<number>(1);
  
  const toggleHeaderCard = () => {
    setIsHeaderCardExpanded(!isHeaderCardExpanded);
  };
  
  const addNewHeader = () => {
    const newId = headers.length > 0 ? Math.max(...headers.map(h => h.id)) + 1 : 1;
    const newHeader: HeaderItem = {
      id: newId,
      name: `Header ${newId}`,
      template: 'default',
      content: `MIME-Version: 1.0
Message-Id: <[a_7].[n_5].[n_3].[a_3]@[domain]>
From: [a_5] <[a_7]@[domain]>
Subject: [ip] [server] [an_5]
Reply-To: reply_to@[domain]
To: [email]
Content-Transfer-Encoding: [content_transfer_encoding]
Content-Type: [content_type]; charset=[charset]
Date: [mail_date]`
    };
    setHeaders([...headers, newHeader]);
    setActiveHeaderId(newId);
  };
  
  const removeHeader = (id: number) => {
    if (headers.length <= 1) return; // Don't remove the last header
    
    const newHeaders = headers.filter(header => header.id !== id);
    setHeaders(newHeaders);
    
    // If the active header was removed, set active to the first available
    if (activeHeaderId === id) {
      setActiveHeaderId(newHeaders[0].id);
    }
  };
  
  const updateHeaderContent = (content: string) => {
    setHeaders(headers.map(header => 
      header.id === activeHeaderId ? { ...header, content } : header
    ));
  };
  
  const updateHeaderTemplate = (template: string) => {
    setHeaders(headers.map(header => 
      header.id === activeHeaderId ? { ...header, template } : header
    ));
  };
  
  const renameHeader = (id: number, newName: string) => {
    setHeaders(headers.map(header => 
      header.id === id ? { ...header, name: newName } : header
    ));
  };
  
  // State for managing multiple creatives
  const [creatives, setCreatives] = useState<CreativeItem[]>([
    {
      id: 1,
      name: 'Creative 1',
      content: ''
    }
  ]);
  const [activeCreativeId, setActiveCreativeId] = useState<number>(1);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');
  
  // State for refresh animations
  const [isServersRefreshing, setIsServersRefreshing] = useState(false);
  const [isInterfacesRefreshing, setIsInterfacesRefreshing] = useState(false);
  
  const addNewCreative = () => {
    const newId = creatives.length > 0 ? Math.max(...creatives.map(c => c.id)) + 1 : 1;
    const newCreative: CreativeItem = {
      id: newId,
      name: `Creative ${newId}`,
      content: ''
    };
    setCreatives([...creatives, newCreative]);
    setActiveCreativeId(newId);
    
    // Ensure new creative tab is visible by scrolling to the right
    setTimeout(() => {
      if (creativesTabsContainerRef.current) {
        creativesTabsContainerRef.current.scrollLeft = creativesTabsContainerRef.current.scrollWidth;
        checkCreativesScrollIndicators();
      }
    }, 100);
  };
  
  const removeCreative = (id: number) => {
    if (creatives.length <= 1) return; // Don't remove the last creative
    
    const newCreatives = creatives.filter(creative => creative.id !== id);
    setCreatives(newCreatives);
    
    // If the active creative was removed, set active to the first available
    if (activeCreativeId === id) {
      setActiveCreativeId(newCreatives[0].id);
    }
  };
  
  // Function to format template tags without extra indentation
  const formatCompactTemplate = (content: string) => {
    // First pass: handle conditional tags {% if/else/endif %}
    let formattedContent = content.replace(/{%\s*(.*?)\s*%}/g, '{% $1 %}')
    
    // Second pass: handle expression tags {{ variable }}
    formattedContent = formattedContent.replace(/{{\s*(.*?)\s*}}/g, '{{ $1 }}')
    
    return formattedContent
  }

  // Function to apply tracking to HTML content
  const applyTrackingToContent = (content: string) => {
    // If content is empty, return as is
    if (!content || content.trim() === '') {
      return content;
    }
    
    // If tracking already exists, return content unchanged
    if (content.includes('{% if tracking_links %}')) {
      return content;
    }
    
    // Clean up any existing body/html closing tags
    let cleanedContent = content;
    if (cleanedContent.includes('</body>')) {
      cleanedContent = cleanedContent.replace('</body>\n</html>', '');
      cleanedContent = cleanedContent.replace('</body></html>', '');
      cleanedContent = cleanedContent.replace('</body>', '');
      cleanedContent = cleanedContent.replace('</html>', '');
    }
    
    // Return content without adding closing tags
    return cleanedContent;
  };

  // Function to update creative content with proper formatting
  const updateCreativeContent = (content: string) => {
    // Always apply template formatting to handle any template tags consistently
    if (content.includes('{%') || content.includes('{{')) {
      content = formatCompactTemplate(content);
    }
    
    // Apply tracking automatically
    content = applyTrackingToContent(content);
    
    // Update the active creative with formatted content
    setCreatives(creatives.map(creative => 
      creative.id === activeCreativeId ? { ...creative, content } : creative
    ));
  };
  
  const activeHeader = headers.find(h => h.id === activeHeaderId) || headers[0];
  const activeCreative = creatives.find(c => c.id === activeCreativeId) || creatives[0];
  
  const handleRefreshPreview = useCallback(() => {
    if (activeCreative) {
      setIsPreviewLoading(true);
      
      // Simulate network request
      setTimeout(() => {
        setPreviewContent(activeCreative.content);
        setIsPreviewLoading(false);
        
        // Re-inject link highlighter after preview updates
        setTimeout(() => {
          const iframe = document.querySelector('iframe[title="HTML Preview"]') as HTMLIFrameElement;
          if (iframe && iframe.contentDocument) {
            const links = iframe.contentDocument.querySelectorAll('a[href]');
            if (links.length > 0 && !iframe.contentDocument.querySelector('.jump-to-link-icon')) {
              // If links exist but don't have icons, reinject
              const event = new Event('load');
              iframe.dispatchEvent(event);
            }
          }
        }, 300);
      }, 300);
    }
  }, [activeCreative]);

  const handleClearHtml = useCallback(() => {
    // Clear the content
    updateCreativeContent("");
  }, [updateCreativeContent]);

  // Add effect to update preview when active creative changes
  useEffect(() => {
    if (activeCreative) {
      setIsPreviewLoading(true);
      
      // Small delay to show loading state
      setTimeout(() => {
        setPreviewContent(activeCreative.content);
        setIsPreviewLoading(false);
      }, 300);
    }
  }, [activeCreativeId]); // Only run when switching creatives, not when content changes

  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const creativesTabsContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(false);
  const [showCreativesLeftScroll, setShowCreativesLeftScroll] = useState(false);
  const [showCreativesRightScroll, setShowCreativesRightScroll] = useState(false);
  
  // Check if scrolling indicators should be shown
  const checkScrollIndicators = () => {
    if (!tabsContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = tabsContainerRef.current;
    
    setShowLeftScroll(scrollLeft > 0);
    setShowRightScroll(scrollLeft < scrollWidth - clientWidth - 5); // 5px buffer
  };
  
  // Check if scrolling indicators should be shown for creatives tabs
  const checkCreativesScrollIndicators = () => {
    if (!creativesTabsContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = creativesTabsContainerRef.current;
    
    // For right-aligned tabs, we need to consider the opposite scroll direction
    setShowCreativesLeftScroll(scrollLeft < scrollWidth - clientWidth - 5); // Right becomes left for right-aligned
    setShowCreativesRightScroll(scrollLeft > 0); // Left becomes right for right-aligned
  };
  
  // Scroll tabs left or right
  const scrollTabs = (direction: 'left' | 'right') => {
    if (!tabsContainerRef.current) return;
    
    const container = tabsContainerRef.current;
    const scrollAmount = 150; // Adjust scroll amount as needed
    
    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  // Scroll creative tabs left or right (directions are reversed for right-aligned tabs)
  const scrollCreativeTabs = (direction: 'left' | 'right') => {
    if (!creativesTabsContainerRef.current) return;
    
    const container = creativesTabsContainerRef.current;
    const scrollAmount = 150; // Adjust scroll amount as needed
    
    // Directions are reversed because tabs are right-aligned
    if (direction === 'left') {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' }); // Scroll right
    } else {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' }); // Scroll left
    }
  };

  // Initialize scroll indicators and add event listener
  useEffect(() => {
    const container = tabsContainerRef.current;
    if (container) {
      checkScrollIndicators();
      container.addEventListener('scroll', checkScrollIndicators);
      
      // Set up a resize observer to check indicators when container size changes
      const resizeObserver = new ResizeObserver(() => {
        checkScrollIndicators();
      });
      
      resizeObserver.observe(container);
      
      return () => {
        container.removeEventListener('scroll', checkScrollIndicators);
        resizeObserver.disconnect();
      };
    }
  }, [headers.length]); // Recheck when headers array changes
  
  // Initialize scroll indicators for creative tabs
  useEffect(() => {
    const container = creativesTabsContainerRef.current;
    if (container) {
      checkCreativesScrollIndicators();
      container.addEventListener('scroll', checkCreativesScrollIndicators);
      
      // Set up a resize observer to check indicators when container size changes
      const resizeObserver = new ResizeObserver(() => {
        checkCreativesScrollIndicators();
      });
      
      resizeObserver.observe(container);
      
      return () => {
        container.removeEventListener('scroll', checkCreativesScrollIndicators);
        resizeObserver.disconnect();
      };
    }
  }, [creatives.length]); // Recheck when creatives array changes
  
  // Make sure current tab is visible when active tab changes
  useEffect(() => {
    if (!tabsContainerRef.current) return;
    
    const container = tabsContainerRef.current;
    const activeTabElement = container.querySelector(`[data-header-id="${activeHeaderId}"]`);
    
    if (activeTabElement) {
      // Calculate position to center the tab
      const tabRect = activeTabElement.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      
      const isTabVisible = tabRect.left >= containerRect.left && tabRect.right <= containerRect.right;
      
      if (!isTabVisible) {
        const centerPosition = tabRect.left + tabRect.width / 2 - containerRect.width / 2;
        container.scrollTo({
          left: container.scrollLeft + (tabRect.left - containerRect.left),
          behavior: 'smooth'
        });
      }
    }
  }, [activeHeaderId]);

  // Make sure current creative tab is visible when active tab changes
  useEffect(() => {
    if (!creativesTabsContainerRef.current) return;
    
    const container = creativesTabsContainerRef.current;
    const activeTabElement = container.querySelector(`[data-creative-id="${activeCreativeId}"]`);
    
    if (activeTabElement) {
      // Calculate position to center the tab
      const tabRect = activeTabElement.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      
      const isTabVisible = tabRect.left >= containerRect.left && tabRect.right <= containerRect.right;
      
      if (!isTabVisible) {
        container.scrollTo({
          left: container.scrollLeft + (tabRect.left - containerRect.left),
          behavior: 'smooth'
        });
      }
    }
  }, [activeCreativeId]);

  // Initialize creative tabs to start from the right
  useEffect(() => {
    if (!creativesTabsContainerRef.current) return;
    
    // Set initial scroll position to the right
    const container = creativesTabsContainerRef.current;
    container.scrollLeft = container.scrollWidth;
  }, []); // Empty dependency array ensures this runs only once on mount

  // State for line numbers in code editor
  const [lineCount, setLineCount] = useState<number>(1);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  
  // Add state for tracking HTML content size
  const [htmlSize, setHtmlSize] = useState<string>("0 KB");
  
  // Debounce function to limit performance impact of expensive operations
  const useDebounce = (fn: Function, delay: number) => {
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    
    return useCallback((...args: any[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        fn(...args);
      }, delay);
    }, [fn, delay]);
  };
  
  // Function to format bytes to human-readable format
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return "0 KB";
    
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  
  // Debounced functions for expensive operations
  const updateMetadataDebounced = useDebounce((content: string) => {
    // Update line count
    const lines = content.split('\n').length;
    setLineCount(Math.max(lines, 1));
    
    // Update size
    const bytes = new Blob([content]).size;
    setHtmlSize(formatBytes(bytes));
  }, 300); // 300ms debounce
  
  // Update content size when active creative changes and observe content changes
  useEffect(() => {
    if (activeCreative && activeCreative.content) {
      // Initial update without debounce when switching creatives
      const lines = activeCreative.content.split('\n').length;
      setLineCount(Math.max(lines, 1));
      
      const bytes = new Blob([activeCreative.content]).size;
      setHtmlSize(formatBytes(bytes));
    } else {
      setLineCount(1);
      setHtmlSize("0 KB");
    }
    
    // Set up scroll sync between textarea and line numbers
    const textarea = textAreaRef.current;
    const lineNumbers = document.getElementById('line-numbers');
    
    if (textarea && lineNumbers) {
      const handleScroll = () => {
        lineNumbers.scrollTop = textarea.scrollTop;
      };
      
      textarea.addEventListener('scroll', handleScroll);
      
      return () => {
        textarea.removeEventListener('scroll', handleScroll);
      };
    }
  }, [activeCreativeId, activeCreative]); // Run when switching creatives or content changes

  // Add this function for rendering optimized line numbers
  const renderLineNumbers = useCallback(() => {
    // Optimization to only render visible line numbers
    if (lineCount <= 100) {
      // For small files, render all line numbers normally
      return Array.from({ length: lineCount }).map((_, i) => (
        <div key={i} className="select-none pr-1 h-[1.5rem] leading-[1.5rem]">{i + 1}</div>
      ));
    } else {
      // For larger files, use a more efficient approach
      // Create a single string with all line numbers to avoid creating many DOM elements
      return (
        <div className="whitespace-pre-line select-none pr-1 leading-[1.5rem]">
          {Array.from({ length: lineCount }).map((_, i) => `${i + 1}\n`).join('')}
        </div>
      );
    }
  }, [lineCount]);

  // State for available servers
  const [availableServers, setAvailableServers] = useState<ServerItem[]>([
    { id: 1, name: "server-1.domain.com", isSelected: false },
    { id: 2, name: "server-2.domain.com", isSelected: false },
    { id: 3, name: "server-3.domain.com", isSelected: false },
    { id: 4, name: "server-4.domain.com (test example)", isSelected: false },
    { id: 5, name: "server-5.domain.com", isSelected: false },
    { id: 6, name: "server-6.domain.com", isSelected: false },
    { id: 7, name: "server-7.domain.com", isSelected: false },
    { id: 8, name: "server-8.domain.com", isSelected: false },
    { id: 9, name: "server-9.domain.com", isSelected: false },
    { id: 10, name: "server-10.domain.com", isSelected: false },
    { id: 11, name: "server-11.domain.com", isSelected: false },
    { id: 12, name: "server-12.domain.com", isSelected: false },
    { id: 13, name: "server-13.domain.com", isSelected: false },
  ]);

  // State for selected servers
  const [selectedServers, setSelectedServers] = useState<ServerItem[]>([
    { id: 101, name: "selected-server-1.domain.com", isSelected: false },
    { id: 102, name: "selected-server-2.domain.com", isSelected: false },
    { id: 103, name: "selected-server-3.domain.com", isSelected: false },
  ]);

  // Toggle selection of a single server
  const toggleServerSelection = (serverId: number) => {
    setAvailableServers(availableServers.map(server => 
      server.id === serverId ? { ...server, isSelected: !server.isSelected } : server
    ));
  };

  // Select all servers
  const selectAllServers = () => {
    setAvailableServers(availableServers.map(server => ({ ...server, isSelected: true })));
  };

  // Deselect all servers
  const deselectAllServers = () => {
    setAvailableServers(availableServers.map(server => ({ ...server, isSelected: false })));
  };

  // Get count of selected available servers
  const selectedAvailableCount = availableServers.filter(server => server.isSelected).length;

  // Transfer functions for moving servers between lists
  const transferSelectedToRight = () => {
    // Find all selected servers from available list
    const serversToMove = availableServers.filter(server => server.isSelected);
    
    if (serversToMove.length === 0) return;
    
    // Add them to selected list
    setSelectedServers([
      ...selectedServers,
      ...serversToMove.map(server => ({ ...server, isSelected: false }))
    ]);
    
    // Remove them from available list
    setAvailableServers(availableServers.filter(server => !server.isSelected));
  };

  const transferAllToRight = () => {
    // Move all servers to selected list
    setSelectedServers([
      ...selectedServers,
      ...availableServers.map(server => ({ ...server, isSelected: false }))
    ]);
    
    // Clear available list
    setAvailableServers([]);
  };

  const transferSelectedToLeft = () => {
    // Find all selected servers from selected list
    const serversToMove = selectedServers.filter(server => server.isSelected);
    
    if (serversToMove.length === 0) return;
    
    // Add them to available list
    setAvailableServers([
      ...availableServers,
      ...serversToMove.map(server => ({ ...server, isSelected: false }))
    ]);
    
    // Remove them from selected list
    setSelectedServers(selectedServers.filter(server => !server.isSelected));
  };

  const transferAllToLeft = () => {
    // Move all servers to available list
    setAvailableServers([
      ...availableServers,
      ...selectedServers.map(server => ({ ...server, isSelected: false }))
    ]);
    
    // Clear selected list
    setSelectedServers([]);
  };

  // Toggle selection of a server in Selected list
  const toggleSelectedServerSelection = (serverId: number) => {
    setSelectedServers(selectedServers.map(server => 
      server.id === serverId ? { ...server, isSelected: !server.isSelected } : server
    ));
  };

  // Get count of selected servers in the Selected list
  const selectedFromSelectedCount = selectedServers.filter(server => server.isSelected).length;

  // Select all servers in the Selected list
  const selectAllSelectedServers = () => {
    setSelectedServers(selectedServers.map(server => ({ ...server, isSelected: true })));
  };

  // Deselect all servers in the Selected list
  const deselectAllSelectedServers = () => {
    setSelectedServers(selectedServers.map(server => ({ ...server, isSelected: false })));
  };

  // State for available interfaces
  const [availableInterfaces, setAvailableInterfaces] = useState<ServerItem[]>([
    { id: 301, name: "interface-test: 10.0.0.1 (test example)", isSelected: false },
  ]);

  // State for selected interfaces
  const [selectedInterfaces, setSelectedInterfaces] = useState<ServerItem[]>([
    { id: 201, name: "interface-1: 192.168.1.1", isSelected: false },
    { id: 202, name: "interface-2: 192.168.1.2", isSelected: false },
    { id: 203, name: "interface-3: 192.168.1.3", isSelected: false },
  ]);

  // Toggle selection of an interface
  const toggleInterfaceSelection = (interfaceId: number) => {
    setSelectedInterfaces(selectedInterfaces.map(iface => 
      iface.id === interfaceId ? { ...iface, isSelected: !iface.isSelected } : iface
    ));
  };

  // Select all interfaces
  const selectAllInterfaces = () => {
    setAvailableInterfaces(availableInterfaces.map(iface => ({ ...iface, isSelected: true })));
  };

  // Deselect all interfaces
  const deselectAllInterfaces = () => {
    setAvailableInterfaces(availableInterfaces.map(iface => ({ ...iface, isSelected: false })));
  };

  // Select all in selected interfaces
  const selectAllSelectedInterfaces = () => {
    setSelectedInterfaces(selectedInterfaces.map(iface => ({ ...iface, isSelected: true })));
  };

  // Deselect all in selected interfaces
  const deselectAllSelectedInterfaces = () => {
    setSelectedInterfaces(selectedInterfaces.map(iface => ({ ...iface, isSelected: false })));
  };

  // Get count of selected available interfaces
  const selectedAvailableInterfacesCount = availableInterfaces.filter(iface => iface.isSelected).length;

  // Get count of selected interfaces in the Selected list
  const selectedFromSelectedInterfacesCount = selectedInterfaces.filter(iface => iface.isSelected).length;

  // Transfer functions for interfaces
  const transferSelectedInterfacesToRight = () => {
    // Find all selected interfaces from available list
    const interfacesToMove = availableInterfaces.filter(iface => iface.isSelected);
    
    if (interfacesToMove.length === 0) return;
    
    // Add them to selected list
    setSelectedInterfaces([
      ...selectedInterfaces,
      ...interfacesToMove.map(iface => ({ ...iface, isSelected: false }))
    ]);
    
    // Remove them from available list
    setAvailableInterfaces(availableInterfaces.filter(iface => !iface.isSelected));
  };

  const transferAllInterfacesToRight = () => {
    // Move all interfaces to selected list
    if (availableInterfaces.length === 0) return;
    
    setSelectedInterfaces([
      ...selectedInterfaces,
      ...availableInterfaces.map(iface => ({ ...iface, isSelected: false }))
    ]);
    
    // Clear available list
    setAvailableInterfaces([]);
  };

  const transferSelectedInterfacesToLeft = () => {
    // Find all selected interfaces from selected list
    const interfacesToMove = selectedInterfaces.filter(iface => iface.isSelected);
    
    if (interfacesToMove.length === 0) return;
    
    // Add them to available list
    setAvailableInterfaces([
      ...availableInterfaces,
      ...interfacesToMove.map(iface => ({ ...iface, isSelected: false }))
    ]);
    
    // Remove them from selected list
    setSelectedInterfaces(selectedInterfaces.filter(iface => !iface.isSelected));
  };

  const transferAllInterfacesToLeft = () => {
    // Move all interfaces to available list
    if (selectedInterfaces.length === 0) return;
    
    setAvailableInterfaces([
      ...availableInterfaces,
      ...selectedInterfaces.map(iface => ({ ...iface, isSelected: false }))
    ]);
    
    // Clear selected list
    setSelectedInterfaces([]);
  };

  // Add search functionality for HTML editor
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchResults, setSearchResults] = useState<{count: number, current: number}>({count: 0, current: 0});
  const [isSearchActive, setIsSearchActive] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [lastCaretPosition, setLastCaretPosition] = useState<{start: number, end: number} | null>(null);
  
  // Function to focus search input
  const focusSearchInput = useCallback(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);
  
  // Function to find all occurrences of a string in text
  const findAllOccurrences = (text: string, searchStr: string): number[] => {
    const positions: number[] = [];
    let pos = text.indexOf(searchStr);
    
    while (pos !== -1) {
      positions.push(pos);
      pos = text.indexOf(searchStr, pos + 1);
    }
    
    return positions;
  };
  
  // Function to navigate to a specific search result
  const navigateToResult = useCallback((index: number) => {
    if (!searchTerm.trim() || !textAreaRef.current) return;
    
    const textarea = textAreaRef.current;
    const content = textarea.value;
    
    // Find all occurrences
    const occurrences = findAllOccurrences(content, searchTerm);
    
    if (occurrences.length > 0) {
      // Ensure index is within bounds
      const targetIndex = Math.max(0, Math.min(index, occurrences.length - 1));
      
      setIsSearchActive(true);
      setSearchResults({count: occurrences.length, current: targetIndex});
      
      // Get the position to highlight
      const position = occurrences[targetIndex];
      
      // Jump to and highlight the occurrence
      textarea.focus();
      textarea.setSelectionRange(position, position + searchTerm.length);
      setLastCaretPosition({start: position, end: position + searchTerm.length});
      
      // Ensure the found text is visible in the viewport
      const lineHeight = 24; // 1.5rem
      const linesBeforeMatch = content.substring(0, position).split('\n').length - 1;
      const scrollPosition = linesBeforeMatch * lineHeight;
      
      textarea.scrollTop = scrollPosition - 100; // Scroll a bit above to provide context
    } else {
      // No results found
      setSearchResults({count: 0, current: 0});
      setIsSearchActive(false);
      setLastCaretPosition(null);
    }
  }, [searchTerm]);
  
  // Function to highlight and jump to found text
  const handleSearch = useCallback(() => {
    navigateToResult(0); // Start from the first result
  }, [navigateToResult]);
  
  // Jump to first result
  const jumpToFirstResult = useCallback(() => {
    navigateToResult(0);
  }, [navigateToResult]);
  
  // Jump to last result
  const jumpToLastResult = useCallback(() => {
    if (!searchTerm.trim() || !textAreaRef.current) return;
    
    const content = textAreaRef.current.value;
    const occurrences = findAllOccurrences(content, searchTerm);
    
    if (occurrences.length > 0) {
      navigateToResult(occurrences.length - 1);
    }
  }, [navigateToResult, searchTerm]);
  
  // Function to navigate to the next search result
  const handleNextResult = useCallback(() => {
    if (searchResults.count === 0) return;
    
    const nextIndex = (searchResults.current + 1) % searchResults.count;
    navigateToResult(nextIndex);
  }, [searchResults, navigateToResult]);
  
  // Function to navigate to the previous search result
  const handlePrevResult = useCallback(() => {
    if (searchResults.count === 0) return;
    
    const prevIndex = (searchResults.current - 1 + searchResults.count) % searchResults.count;
    navigateToResult(prevIndex);
  }, [searchResults, navigateToResult]);
  
  // Effect to maintain highlights during editing
  useEffect(() => {
    if (isSearchActive && lastCaretPosition && textAreaRef.current) {
      const textarea = textAreaRef.current;
      
      // If user is typing, we'll restore the search after a delay
      const timeoutId = setTimeout(() => {
        // Check if we still have the search term
        if (searchTerm.trim() && textarea.value.includes(searchTerm)) {
          const content = textarea.value;
          const occurrences = findAllOccurrences(content, searchTerm);
          
          if (occurrences.length > 0) {
            // Try to find the nearest occurrence to the current cursor position
            const cursorPos = textarea.selectionStart;
            let closestIndex = 0;
            let closestDistance = Infinity;
            
            occurrences.forEach((pos, idx) => {
              const distance = Math.abs(pos - cursorPos);
              if (distance < closestDistance) {
                closestDistance = distance;
                closestIndex = idx;
              }
            });
            
            // Update the search state with new counts
            setSearchResults({
              count: occurrences.length,
              current: closestIndex
            });
          }
        }
      }, 300);
      
      return () => clearTimeout(timeoutId);
    }
  }, [isSearchActive, lastCaretPosition, searchTerm, activeCreative.content]);
  
  // Effect to reset search results when search term changes
  useEffect(() => {
    setSearchResults({count: 0, current: 0});
    setIsSearchActive(false);
    setLastCaretPosition(null);
  }, [searchTerm]);
  
  // Handle Enter key in search box
  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // Prevent form submission if within a form
      if (e.shiftKey) {
        handlePrevResult();
      } else {
        if (isSearchActive) {
          handleNextResult();
        } else {
          handleSearch();
        }
      }
    } else if (e.key === 'Escape') {
      // Clear search on Escape key
      setSearchTerm('');
      setIsSearchActive(false);
      setSearchResults({count: 0, current: 0});
      setLastCaretPosition(null);
    }
  };

  // Add keyboard shortcut for search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+F or Cmd+F to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault(); // Prevent browser's default search
        focusSearchInput();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [focusSearchInput]);
  
  // Modal state for generate links
  const [isLinksModalOpen, setIsLinksModalOpen] = useState(false);
  const [extractedLinks, setExtractedLinks] = useState<Array<{
    id: number;
    url: string;
    type: string;
    tag: string;
  }>>([]);
  
  // Function to extract links from HTML content
  const extractLinksFromHtml = useCallback((html: string) => {
    if (!html) return [];
    
    try {
      console.log("Starting link extraction process");
      const links: Array<{
        id: number;
        url: string;
        type: string;
        tag: string;
      }> = [];
      
      let id = 1;
      
      // First attempt: Use DOMParser (works in browsers)
      if (typeof window !== 'undefined') {
        try {
          console.log("Using DOM parser approach");
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          
          // Extract anchor links
          const anchors = doc.querySelectorAll('a[href]');
          anchors.forEach((anchor) => {
            const href = anchor.getAttribute('href');
            if (href) {
              links.push({
                id: id++,
                url: href,
                type: 'link',
                tag: 'Click' // Default tag
              });
              console.log(`Found link via DOM: ${href}`);
            }
          });
          
          // Extract image links
          const images = doc.querySelectorAll('img[src]');
          images.forEach((img) => {
            const src = img.getAttribute('src');
            if (src) {
              links.push({
                id: id++,
                url: src,
                type: 'image',
                tag: ''
              });
              console.log(`Found image via DOM: ${src}`);
            }
          });
        } catch (e) {
          console.error("DOM parser approach failed:", e);
        }
      }
      
      // If DOM parsing didn't find anything or isn't available, try regex as fallback
      if (links.length === 0) {
        console.log("Using regex approach as fallback");
        
        // More robust regex patterns to handle different quote styles and HTML structures
        // Pattern for anchor tags with href attributes (handles both single and double quotes)
        const linkPattern = /<a\s+(?:[^>]*?\s+)?href=(['"])(.*?)\1[^>]*>[\s\S]*?<\/a>/gi;
        // Pattern for image tags with src attributes (handles both single and double quotes)
        const imagePattern = /<img\s+(?:[^>]*?\s+)?src=(['"])(.*?)\1[^>]*?>/gi;
        
        // Process anchor tags
        let linkMatch;
        while ((linkMatch = linkPattern.exec(html)) !== null) {
          if (linkMatch[2]) {
            links.push({
              id: id++,
              url: linkMatch[2],
              type: 'link',
              tag: 'Click' // Default tag
            });
            console.log(`Found link via regex: ${linkMatch[2]}`);
          }
        }
        
        // Process image tags
        let imgMatch;
        while ((imgMatch = imagePattern.exec(html)) !== null) {
          if (imgMatch[2]) {
            links.push({
              id: id++,
              url: imgMatch[2],
              type: 'image',
              tag: ''
            });
            console.log(`Found image via regex: ${imgMatch[2]}`);
          }
        }
      }
      
      console.log(`Total links extracted: ${links.length}`);
      return links;
    } catch (error) {
      console.error('Error extracting links:', error);
      return [];
    }
  }, []);
  
  // Function to handle opening the generate links modal
  const handleGenerateLinks = useCallback(() => {
    if (activeCreative && activeCreative.content) {
      try {
        console.log('Attempting to extract links from HTML content');
        const links = extractLinksFromHtml(activeCreative.content);
        setExtractedLinks(links);
        setIsLinksModalOpen(true);
        
        // Always open modal even if no links found, without showing any alert
        if (links.length === 0) {
          console.warn('No links found in the HTML content');
        } else {
          console.log(`Successfully extracted ${links.length} links`);
        }
      } catch (error) {
        console.error('Failed to generate links:', error);
        alert('Failed to extract links. Please check your HTML content and try again.');
      }
    } else {
      console.warn('No content to extract links from');
      alert('No HTML content to extract links from. Please add some content first.');
    }
  }, [activeCreative, extractLinksFromHtml]);
  
  // Function to update link tag type
  const updateLinkTag = useCallback((id: number, tag: string) => {
    setExtractedLinks(prev => 
      prev.map(link => 
        link.id === id ? { ...link, tag } : link
      )
    );
  }, []);
  
  // Function to copy link tags to clipboard
  const copyLinksToClipboard = useCallback(() => {
    // Format links for clipboard
    const linksText = extractedLinks
      .filter(link => link.type === 'link' && link.tag)
      .map(link => `${link.url} [${link.tag}]`)
      .join('\n');
    
    // Add image links separately
    const imageText = extractedLinks
      .filter(link => link.type === 'image')
      .map(link => link.url)
      .join('\n');
    
    // Combine texts with a header for each section
    const clipboardText = 
      `--- LINKS WITH TAGS ---\n${linksText || 'No links found'}\n\n` + 
      `--- IMAGE LINKS ---\n${imageText || 'No images found'}`;
    
    navigator.clipboard.writeText(clipboardText)
      .then(() => {
        // Show success message in console
        console.log('Links copied to clipboard successfully');
        alert('Links copied to clipboard successfully!');
        
        // Close modal after successful copy
        setIsLinksModalOpen(false);
      })
      .catch(err => {
        console.error('Failed to copy links: ', err);
        alert('Failed to copy links. See console for details.');
      });
  }, [extractedLinks]);
  
  // Add a function to insert test HTML with links
  const handleInsertTestHtml = useCallback(() => {
    // Sample HTML with various link formats
    const testHtml = `<!DOCTYPE html>
<html>
<head>
  <title>Link Test Page</title>
</head>
<body>
  <h1>Email Newsletter</h1>
  <p>Hello subscriber! Check out our latest offers and updates below:</p>
  
  <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h2 style="color: #0d6efd;">Special Offers</h2>
    <ul>
      <li><a href="https://example.com/summer-sale">Summer Sale - Up to 50% off!</a></li>
      <li><a href="https://example.com/new-arrivals">Check out our new arrivals</a></li>
      <li><a href="https://example.com/exclusive">Exclusive member offers</a></li>
    </ul>
    <img src="https://example.com/banner.jpg" alt="Summer Sale Banner" style="width: 100%; max-width: 600px;">
  </div>
  
  <div style="padding: 15px; border: 1px solid #dee2e6; margin: 20px 0;">
    <h2 style="color: #198754;">Latest Articles</h2>
    <div>
      <h3>How to Make the Most of Your Summer</h3>
      <p>Read our tips on summer activities and more...</p>
      <a href="https://example.com/blog/summer-tips">Read more</a>
    </div>
    <hr style="margin: 15px 0;">
    <div>
      <h3>10 Must-Visit Destinations</h3>
      <p>Discover the best places to visit this year...</p>
      <a href="https://example.com/blog/destinations">Read more</a>
    </div>
  </div>
  
  <p>If you no longer wish to receive these emails, <a href="https://example.com/unsubscribe">unsubscribe here</a>.</p>
  <p>To manage your preferences, <a href="https://example.com/preferences">click here</a>.</p>
  
  <div style="margin-top: 30px; text-align: center;">
    <a href="https://example.com/facebook"><img src="https://example.com/facebook-icon.png" alt="Facebook"></a>
    <a href="https://example.com/twitter"><img src="https://example.com/twitter-icon.png" alt="Twitter"></a>
    <a href="https://example.com/instagram"><img src="https://example.com/instagram-icon.png" alt="Instagram"></a>
  </div>
</body>
</html>`;

    // Update the creative content
    updateCreativeContent(testHtml);
    
    // Update preview after a short delay
    setTimeout(() => {
      handleRefreshPreview();
    }, 100);
  }, [updateCreativeContent, handleRefreshPreview]);

  // Function to replace links in HTML content with tagged versions
  const replaceLinksInHtml = useCallback(() => {
    if (!activeCreative || !activeCreative.content) {
      alert('No HTML content to modify.');
      return;
    }
    
    try {
      // Start with the current HTML content
      let newContent = activeCreative.content;
      
      // Sort links by length (descending) to avoid replacing parts of longer URLs first
      const sortedLinks = [...extractedLinks]
        .filter(link => link.type === 'link' && link.tag)
        .sort((a, b) => b.url.length - a.url.length);
      
      // Replace each tagged link with its replacement
      for (const link of sortedLinks) {
        // Skip links without tags
        if (!link.tag) continue;
        
        // Create a regex that matches the exact URL
        const escapedUrl = link.url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(href=["'])${escapedUrl}(["'])`, 'g');
        
        // Replace the URL with the tagged version
        newContent = newContent.replace(regex, `$1${link.url}?tag=${link.tag.toLowerCase()}$2`);
      }
      
      // Update the creative content with the modified HTML
      updateCreativeContent(newContent);
      
      // Close the modal
      setIsLinksModalOpen(false);
      
      // Show success message
      alert(`Successfully replaced ${sortedLinks.length} links with tagged versions.`);
      
      // Update preview
      handleRefreshPreview();
    } catch (error) {
      console.error('Error replacing links:', error);
      alert('Failed to replace links. Please check console for details.');
    }
  }, [activeCreative, extractedLinks, updateCreativeContent, handleRefreshPreview]);

  // Function to generate links (text format) for copying
  const generateLinks = useCallback(() => {
    // Format links for clipboard
    const linksText = extractedLinks
      .filter(link => link.type === 'link' && link.tag)
      .map(link => `${link.url} [${link.tag}]`)
      .join('\n');
    
    // Add image links separately
    const imageText = extractedLinks
      .filter(link => link.type === 'image')
      .map(link => link.url)
      .join('\n');
    
    // Combine texts with a header for each section
    const clipboardText = 
      `--- LINKS WITH TAGS ---\n${linksText || 'No links found'}\n\n` + 
      `--- IMAGE LINKS ---\n${imageText || 'No images found'}`;
    
    navigator.clipboard.writeText(clipboardText)
      .then(() => {
        // Show success message in console
        console.log('Links copied to clipboard successfully');
        alert('Links copied to clipboard successfully!');
      })
      .catch(err => {
        console.error('Failed to copy links: ', err);
        alert('Failed to copy links. See console for details.');
      });
  }, [extractedLinks]);

  // Function to find position of a link in HTML content
  const findLinkPosition = useCallback((html: string, url: string): number => {
    // Try multiple approaches to find the link

    // 1. Try finding the href attribute with this URL (handling both single and double quotes)
    const escapedUrl = url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // Handle both single and double quotes with case-insensitive search
    const doubleQuotePattern = new RegExp(`href=["']${escapedUrl}["']`, 'i');
    const doubleQuoteMatch = doubleQuotePattern.exec(html);
    
    if (doubleQuoteMatch && doubleQuoteMatch.index >= 0) {
      return doubleQuoteMatch.index;
    }
    
    // 2. Try finding the URL directly (more permissive)
    const urlIndex = html.indexOf(url);
    if (urlIndex >= 0) {
      return urlIndex;
    }
    
    // 3. If the URL has query parameters, try finding it without those
    const baseUrlMatch = url.match(/^([^?#]+)/);
    if (baseUrlMatch && baseUrlMatch[1] !== url) {
      const baseUrl = baseUrlMatch[1];
      const baseUrlIndex = html.indexOf(baseUrl);
      if (baseUrlIndex >= 0) {
        return baseUrlIndex;
      }
    }
    
    // 4. One last attempt - look for partial match
    if (url.length > 10) {
      // Use a substring of the URL (first 75% of characters)
      const partialLength = Math.floor(url.length * 0.75);
      const partialUrl = url.substring(0, partialLength);
      const partialIndex = html.indexOf(partialUrl);
      if (partialIndex >= 0) {
        return partialIndex;
      }
    }
    
    // Not found
    return -1;
  }, []);

  // Function to jump to a link in the HTML editor
  const jumpToLink = useCallback((url: string) => {
    if (!textAreaRef.current || !activeCreative || !activeCreative.content) {
      console.warn('Cannot jump to link: Editor or content not available');
      return;
    }
    
    console.log('Jumping to link:', url);
    const position = findLinkPosition(activeCreative.content, url);
    console.log('Found position:', position);
    
    if (position >= 0) {
      // First close the links modal if it's open
      setIsLinksModalOpen(false);
      
      // Small delay to ensure the modal is closed and DOM is updated
      setTimeout(() => {
        // Make sure the textarea ref is still valid
        if (!textAreaRef.current) {
          console.warn('Text area reference lost after setTimeout');
          return;
        }
        
        // Focus the textarea
        textAreaRef.current.focus();
        
        // Calculate the line number to scroll to
        const contentBeforeLink = activeCreative.content.substring(0, position);
        const lineNumber = contentBeforeLink.split('\n').length;
        console.log('Scrolling to line:', lineNumber);
        
        // Find the end of the URL in the HTML
        let endPosition = position;
        const maxUrlLength = url.length + 100; // Allow for some extra characters like quotes
        const searchArea = activeCreative.content.substring(position, position + maxUrlLength);
        
        // Look for closing quotes or spaces to determine URL end
        let quoteMatch = searchArea.match(/['"]/);
        if (quoteMatch && quoteMatch.index) {
          endPosition = position + quoteMatch.index + 1;
        } else {
          // If no quote found, assume URL length
          endPosition = position + url.length;
        }
        
        // Create a style element for the selection highlight
        const highlightStyle = document.createElement('style');
        highlightStyle.id = 'highlight-selection-style';
        highlightStyle.textContent = `
          textarea::selection {
            background-color: #ffff00 !important;
            color: #000000 !important;
          }
        `;
        document.head.appendChild(highlightStyle);
        
        // Set cursor position at the link with selection
        textAreaRef.current.setSelectionRange(position, endPosition);
        
        // Scroll to the position (approximate based on line height)
        const lineHeight = 24; // 1.5rem line height
        textAreaRef.current.scrollTop = (lineNumber - 5) * lineHeight; // Scroll to 5 lines above for context
        
        // Remove the highlight style after 3 seconds
        setTimeout(() => {
          const styleElement = document.getElementById('highlight-selection-style');
          if (styleElement && styleElement.parentNode) {
            styleElement.parentNode.removeChild(styleElement);
          }
          
          // Keep the selection active but it will now use default selection color
          if (textAreaRef.current) {
            // Re-focus and re-select to refresh the selection appearance
            textAreaRef.current.focus();
            textAreaRef.current.setSelectionRange(position, endPosition);
          }
        }, 3000);
      }, 100);
    } else {
      console.warn(`Could not find link position for URL: ${url}`);
    }
  }, [findLinkPosition, activeCreative, setIsLinksModalOpen]);

  // Add a hook to enable link jumping from the preview
  useEffect(() => {
    if (!previewContent) return;
    
    // Function to inject link highlight behavior into the iframe
    const injectLinkHighlighter = () => {
      const iframe = document.querySelector('iframe[title="HTML Preview"]') as HTMLIFrameElement;
      if (!iframe || !iframe.contentDocument) return;
      
      const doc = iframe.contentDocument;
      
      // Remove any previous styles to start fresh
      const existingStyles = doc.getElementById('hover-styles');
      if (existingStyles) existingStyles.remove();
      
      // Create direct overlay icons instead of trying to append to links
      const style = doc.createElement('style');
      style.id = 'hover-styles';
      style.textContent = `
        /* Add highlight to all links on hover */
        a:hover {
          outline: 2px solid #3b82f6 !important;
          outline-offset: 2px !important;
          position: relative !important;
          z-index: 5 !important;
        }
        
        /* Style for the fixed jump button container */
        #jump-button-container {
          position: fixed;
          top: 10px;
          right: 10px;
          z-index: 9999;
          display: none;
          transition: all 0.2s ease;
        }
        
        /* Jump button style */
        #jump-button {
          width: 36px;
          height: 36px;
          background-color: #3b82f6;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          transition: transform 0.2s ease;
        }
        
        #jump-button:hover {
          transform: scale(1.1);
          background-color: #2563eb;
        }
      `;
      doc.head.appendChild(style);
      
      // Remove existing button if any
      const existingButton = doc.getElementById('jump-button-container');
      if (existingButton) existingButton.remove();
      
      // Create the jump button container
      const buttonContainer = doc.createElement('div');
      buttonContainer.id = 'jump-button-container';
      
      // Create the jump button
      const jumpButton = doc.createElement('div');
      jumpButton.id = 'jump-button';
      jumpButton.innerHTML = '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M7 17L17 7"></path><path d="M7 7h10v10"></path></svg>';
      jumpButton.title = 'Jump to this link in HTML editor';
      
      // Add to document
      buttonContainer.appendChild(jumpButton);
      doc.body.appendChild(buttonContainer);
      
      // Track current link URL 
      let currentLinkUrl = '';
      
      // Add hover listener to all links
      const links = doc.querySelectorAll('a[href]');
      links.forEach((linkElement: Element) => {
        linkElement.addEventListener('mouseenter', () => {
          const url = linkElement.getAttribute('href');
          if (!url) return;
          
          // Save URL and show button
          currentLinkUrl = url;
          
          // Get link position to position the button near it
          const rect = linkElement.getBoundingClientRect();
          
          // Position the button near the link
          buttonContainer.style.display = 'block';
          buttonContainer.style.top = `${rect.top - 8}px`;
          buttonContainer.style.left = `${rect.right + 8}px`;
        });
        
        linkElement.addEventListener('mouseleave', () => {
          // Hide button with slight delay to allow clicking
          setTimeout(() => {
            if (!buttonContainer.matches(':hover')) {
              buttonContainer.style.display = 'none';
            }
          }, 200);
        });
      });
      
      // Button hover handler to keep it visible when hovered
      buttonContainer.addEventListener('mouseenter', () => {
        buttonContainer.style.display = 'block';
      });
      
      buttonContainer.addEventListener('mouseleave', () => {
        buttonContainer.style.display = 'none';
      });
      
      // Jump button click handler
      jumpButton.addEventListener('click', (e) => {
        e.stopPropagation();
        if (currentLinkUrl) {
          console.log('Jump button clicked for URL:', currentLinkUrl);
          // Use postMessage to communicate with the parent window
          try {
            window.parent.postMessage({ 
              type: 'jumpToLink', 
              url: currentLinkUrl,
              timestamp: Date.now() // Add timestamp to make each message unique
            }, '*');
            buttonContainer.style.display = 'none';
          } catch (err) {
            console.error('Error sending postMessage:', err);
          }
        }
      });
    };
    
    // Add event listener for messages from the iframe
    const handleMessage = (event: MessageEvent) => {
      // Log all incoming messages for debugging
      console.log('Received message:', event.data);
      
      if (event.data && event.data.type === 'jumpToLink') {
        console.log('Jump to link message received with URL:', event.data.url);
        jumpToLink(event.data.url);
        // Modal closing is now handled inside jumpToLink
      }
    };
    
    // Ensure we add the event listener only once
    window.removeEventListener('message', handleMessage);
    window.addEventListener('message', handleMessage);
    
    // Set up multiple ways to ensure icons get applied
    const setupIconHandlers = () => {
      const iframe = document.querySelector('iframe[title="HTML Preview"]') as HTMLIFrameElement;
      if (!iframe) {
        setTimeout(setupIconHandlers, 200);
        return;
      }
      
      // Initial injection after a delay
      setTimeout(injectLinkHighlighter, 500);
      
      // Set up load event
      iframe.addEventListener('load', () => {
        setTimeout(injectLinkHighlighter, 300);
      });
      
      // Regular interval check as fallback
      const intervalId = setInterval(injectLinkHighlighter, 2000);
      
      return () => {
        clearInterval(intervalId);
      };
    };
    
    const cleanup = setupIconHandlers();
    
    return () => {
      window.removeEventListener('message', handleMessage);
      if (cleanup) cleanup();
    };
  }, [previewContent, jumpToLink]);

  // Add this new state for domain selection
  const [selectedDomain, setSelectedDomain] = useState<string>("body_domain");

  // Modal state for generate links
  const [isTrackingLinksModalOpen, setIsTrackingLinksModalOpen] = useState(false)

  // Add a function to handle adding a new tracking link
  const addTrackingLink = () => {
    // This would be implemented to add a new tracking link
    // For now it's just a placeholder
    console.log('Add new tracking link');
  };

  // Add state for WideArea Mode
  const [isWideAreaMode, setIsWideAreaMode] = useState(false);
  
  // Toggle WideArea Mode
  const toggleWideAreaMode = () => {
    setIsWideAreaMode(!isWideAreaMode);
  };

  // Toggle Header WideArea Mode
  const toggleHeaderWideAreaMode = () => {
    setIsHeaderWideAreaMode(true);
  };

  // Toggle Preview WideArea Mode
  const togglePreviewWideAreaMode = () => {
    setIsPreviewWideAreaMode(true);
  };

  // Data Management state
  interface DataItem {
    id: number;
    name: string;
    geo: string;
    esp: string;
    provider: string;
    vertical: string; // Adding new vertical property
    fresh: number;
    clean: number;
    openers: number;
    clickers: number;
    unsub: number;
    lead: number;
    otherGeos: number;
  }
  
  const [dataItems, setDataItems] = useState<DataItem[]>([
    { id: 1, name: 'testlist1', geo: 'cn', esp: 'Hotmail', provider: 'e-impact', vertical: 'Finance', fresh: 2, clean: 0, openers: 0, clickers: 0, unsub: 0, lead: 0, otherGeos: 0 },
    { id: 2, name: 'nl_just1.0.2', geo: 'nl', esp: 'Gmx', provider: 'e-impact', vertical: 'Health', fresh: 59191, clean: 259189, openers: 16122, clickers: 4993, unsub: 1427, lead: 22, otherGeos: 10 },
    { id: 3, name: 'de_test_data', geo: 'de', esp: 'Hotmail', provider: 'e-impact', vertical: 'Education', fresh: 3841, clean: 12841, openers: 2458, clickers: 942, unsub: 127, lead: 5, otherGeos: 0 },
    { id: 4, name: 'mixed_lists', geo: 'nl', esp: 'Gmx', provider: 'e-impact', vertical: 'Finance', fresh: 7842, clean: 21547, openers: 3212, clickers: 1043, unsub: 286, lead: 18, otherGeos: 0 },
    { id: 5, name: 'eu_special', geo: 'de', esp: 'Hotmail', provider: 'e-impact', vertical: 'Health', fresh: 16248, clean: 27851, openers: 4125, clickers: 1358, unsub: 347, lead: 7, otherGeos: 0 },
    { id: 6, name: 'cn_data_01', geo: 'cn', esp: 'Gmx', provider: 'e-impact', vertical: 'Travel', fresh: 8421, clean: 15743, openers: 1876, clickers: 594, unsub: 152, lead: 3, otherGeos: 0 },
    { id: 7, name: 'nl_premium', geo: 'nl', esp: 'Hotmail', provider: 'e-impact', vertical: 'Education', fresh: 12854, clean: 31247, openers: 5241, clickers: 1873, unsub: 412, lead: 29, otherGeos: 0 },
    { id: 8, name: 'de_exclusive', geo: 'de', esp: 'Gmx', provider: 'e-impact', vertical: 'Finance', fresh: 9874, clean: 17426, openers: 2189, clickers: 743, unsub: 198, lead: 14, otherGeos: 0 },
    { id: 9, name: 'cn_special', geo: 'cn', esp: 'Hotmail', provider: 'e-impact', vertical: 'Travel', fresh: 6417, clean: 12874, openers: 1654, clickers: 547, unsub: 124, lead: 2, otherGeos: 0 },
    { id: 10, name: 'nl_fresh', geo: 'nl', esp: 'Gmx', provider: 'e-impact', vertical: 'Health', fresh: 11782, clean: 25943, openers: 3846, clickers: 1287, unsub: 315, lead: 19, otherGeos: 0 },
    { id: 11, name: 'de_business', geo: 'de', esp: 'Hotmail', provider: 'e-impact', vertical: 'Finance', fresh: 14582, clean: 29841, openers: 4125, clickers: 1532, unsub: 427, lead: 31, otherGeos: 5 },
    { id: 12, name: 'cn_premium', geo: 'cn', esp: 'Gmx', provider: 'e-impact', vertical: 'Education', fresh: 7921, clean: 18743, openers: 2876, clickers: 954, unsub: 201, lead: 7, otherGeos: 0 },
    { id: 13, name: 'nl_exclusive', geo: 'nl', esp: 'Hotmail', provider: 'e-impact', vertical: 'Travel', fresh: 10854, clean: 23247, openers: 3241, clickers: 1173, unsub: 342, lead: 15, otherGeos: 0 },
    // Add example countries to demonstrate search functionality
    { id: 14, name: 'es_data', geo: 'es', esp: 'Gmail', provider: 'e-impact', vertical: 'Travel', fresh: 8754, clean: 19247, openers: 2941, clickers: 973, unsub: 242, lead: 12, otherGeos: 0 },
    { id: 15, name: 'fr_data', geo: 'fr', esp: 'Yahoo', provider: 'e-impact', vertical: 'Finance', fresh: 9354, clean: 21547, openers: 3341, clickers: 1173, unsub: 282, lead: 17, otherGeos: 0 },
    { id: 16, name: 'uk_data', geo: 'gb', esp: 'Outlook', provider: 'e-impact', vertical: 'Health', fresh: 7854, clean: 16247, openers: 2541, clickers: 873, unsub: 182, lead: 9, otherGeos: 0 },
    { id: 17, name: 'it_data', geo: 'it', esp: 'Gmx', provider: 'e-impact', vertical: 'Education', fresh: 6854, clean: 15247, openers: 2141, clickers: 773, unsub: 152, lead: 7, otherGeos: 0 },
  ]);

  // Add state for selected cells
  interface SelectedCell {
    itemId: number;
    field: keyof DataItem;
    value: number | string;
  }

  const [selectedCells, setSelectedCells] = useState<SelectedCell[]>([]);

  // Function to handle cell selection
  const toggleCellSelection = (itemId: number, field: keyof DataItem, value: number | string) => {
    // Only allow selection of numeric fields (except id)
    if (field === 'id' || field === 'name' || field === 'geo' || field === 'esp' || field === 'provider' || field === 'vertical') {
      return;
    }

    // Check if cell is already selected
    const existingIndex = selectedCells.findIndex(
      cell => cell.itemId === itemId && cell.field === field
    );

    if (existingIndex >= 0) {
      // Remove from selection if already selected
      setSelectedCells(selectedCells.filter((_, index) => index !== existingIndex));
    } else {
      // Add to selection
      setSelectedCells([...selectedCells, { itemId, field, value }]);
    }
  };

  // Check if a cell is selected
  const isCellSelected = (itemId: number, field: keyof DataItem) => {
    return selectedCells.some(cell => cell.itemId === itemId && cell.field === field);
  };
  
  // Enhanced color palette for the application
  const colorPalette = {
    // Primary colors
    primary: {
      light: 'bg-indigo-50 dark:bg-indigo-900/20',
      medium: 'bg-indigo-100 dark:bg-indigo-800/30',
      border: 'border-indigo-200 dark:border-indigo-800/40',
      hover: 'hover:bg-indigo-100 dark:hover:bg-indigo-800/30',
      text: 'text-indigo-600 dark:text-indigo-400',
      focusBg: 'bg-indigo-500 dark:bg-indigo-600',
      focusText: 'text-white',
    },
    // Secondary / purple
    secondary: {
      light: 'bg-purple-50 dark:bg-purple-900/20',
      medium: 'bg-purple-100 dark:bg-purple-800/30',
      border: 'border-purple-200 dark:border-purple-800/40',
      hover: 'hover:bg-purple-100 dark:hover:bg-purple-800/30',
      text: 'text-purple-600 dark:text-purple-400',
      focusBg: 'bg-purple-500 dark:bg-purple-600',
      focusText: 'text-white',
    },
    // Success / green
    success: {
      light: 'bg-emerald-50 dark:bg-emerald-900/20',
      medium: 'bg-emerald-100 dark:bg-emerald-800/30',
      border: 'border-emerald-200 dark:border-emerald-800/40',
      hover: 'hover:bg-emerald-100 dark:hover:bg-emerald-800/30',
      text: 'text-emerald-600 dark:text-emerald-400',
      focusBg: 'bg-emerald-500 dark:bg-emerald-600',
      focusText: 'text-white',
    },
    // Info / blue
    info: {
      light: 'bg-sky-50 dark:bg-sky-900/20',
      medium: 'bg-sky-100 dark:bg-sky-800/30',
      border: 'border-sky-200 dark:border-sky-800/40',
      hover: 'hover:bg-sky-100 dark:hover:bg-sky-800/30',
      text: 'text-sky-600 dark:text-sky-400',
      focusBg: 'bg-sky-500 dark:bg-sky-600',
      focusText: 'text-white',
    },
    // Warning / amber
    warning: {
      light: 'bg-amber-50 dark:bg-amber-900/20',
      medium: 'bg-amber-100 dark:bg-amber-800/30',
      border: 'border-amber-200 dark:border-amber-800/40',
      hover: 'hover:bg-amber-100 dark:hover:bg-amber-800/30',
      text: 'text-amber-600 dark:text-amber-400',
      focusBg: 'bg-amber-500 dark:bg-amber-600',
      focusText: 'text-white',
    },
    // Danger / red
    danger: {
      light: 'bg-rose-50 dark:bg-rose-900/20',
      medium: 'bg-rose-100 dark:bg-rose-800/30',
      border: 'border-rose-200 dark:border-rose-800/40',
      hover: 'hover:bg-rose-100 dark:hover:bg-rose-800/30',
      text: 'text-rose-600 dark:text-rose-400',
      focusBg: 'bg-rose-500 dark:bg-rose-600',
      focusText: 'text-white',
    },
    // Neutral / slate
    neutral: {
      light: 'bg-slate-50 dark:bg-slate-900/20',
      medium: 'bg-slate-100 dark:bg-slate-800/30',
      border: 'border-slate-200 dark:border-slate-800/40',
      hover: 'hover:bg-slate-100 dark:hover:bg-slate-800/30',
      text: 'text-slate-600 dark:text-slate-400',
      focusBg: 'bg-slate-500 dark:bg-slate-600',
      focusText: 'text-white',
    },
    // Teal - for variety
    teal: {
      light: 'bg-teal-50 dark:bg-teal-900/20',
      medium: 'bg-teal-100 dark:bg-teal-800/30',
      border: 'border-teal-200 dark:border-teal-800/40',
      hover: 'hover:bg-teal-100 dark:hover:bg-teal-800/30',
      text: 'text-teal-600 dark:text-teal-400',
      focusBg: 'bg-teal-500 dark:bg-teal-600',
      focusText: 'text-white',
    },
    // Violet - for variety
    violet: {
      light: 'bg-violet-50 dark:bg-violet-900/20',
      medium: 'bg-violet-100 dark:bg-violet-800/30',
      border: 'border-violet-200 dark:border-violet-800/40',
      hover: 'hover:bg-violet-100 dark:hover:bg-violet-800/30',
      text: 'text-violet-600 dark:text-violet-400',
      focusBg: 'bg-violet-500 dark:bg-violet-600',
      focusText: 'text-white',
    },
    // Cyan - for variety
    cyan: {
      light: 'bg-cyan-50 dark:bg-cyan-900/20',
      medium: 'bg-cyan-100 dark:bg-cyan-800/30',
      border: 'border-cyan-200 dark:border-cyan-800/40',
      hover: 'hover:bg-cyan-100 dark:hover:bg-cyan-800/30',
      text: 'text-cyan-600 dark:text-cyan-400',
      focusBg: 'bg-cyan-500 dark:bg-cyan-600', 
      focusText: 'text-white',
    }
  };
  
  const [sortConfig, setSortConfig] = useState<{
    key: keyof DataItem | null;
    direction: 'asc' | 'desc' | null;
  }>({
    key: null,
    direction: null
  });
  
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Filter states for Data Management
  const [geoFilter, setGeoFilter] = useState<string[] | null>(null);
  const [espFilter, setEspFilter] = useState<string[] | null>(null);
  const [providerFilter, setProviderFilter] = useState<string[] | null>(null);
  const [verticalFilter, setVerticalFilter] = useState<string[] | null>(null);
  const [filtersVisible, setFiltersVisible] = useState<boolean>(false);
  
  // Helper to handle select change values with proper typing
  const handleMultiSelectChange = (setter: React.Dispatch<React.SetStateAction<string[] | null>>) => 
    (value: string | string[]) => {
      if (Array.isArray(value)) {
        setter(value.length > 0 ? value : null);
      } else {
        setter(value ? [value] : null);
      }
      setCurrentPage(1); // Reset to first page when filter changes
    };
  
  // New state variables for searchable selects
  const [bodyNameValue, setBodyNameValue] = useState<string>("");
  const [sponsorValue, setSponsorValue] = useState<string>("");
  const [offerValue, setOfferValue] = useState<string>("");
  const [fromLineTemplateValue, setFromLineTemplateValue] = useState<string>("");
  const [subjectLineTemplateValue, setSubjectLineTemplateValue] = useState<string>("");
  
  // Data arrays for searchable selects
  const bodyTemplates = [
    { value: "template1", label: "Main Template" },
    { value: "template2", label: "Promotional" },
    { value: "template3", label: "Newsletter" },
    { value: "template4", label: "Announcement" },
    { value: "template5", label: "Welcome" },
    { value: "template6", label: "Follow Up" },
    { value: "template7", label: "Reminder" },
    { value: "template8", label: "Confirmation" },
    { value: "template9", label: "Thank You" },
    { value: "template10", label: "Feedback" },
    { value: "template11", label: "Onboarding" },
    { value: "template12", label: "Educational" },
  ];
  
  const sponsors = [
    { value: "sponsor1", label: "Sponsor 1" },
    { value: "sponsor2", label: "Sponsor 2" },
    { value: "sponsor3", label: "Sponsor 3" },
    { value: "sponsor4", label: "Sponsor 4" },
    { value: "sponsor5", label: "Premium Partner" },
    { value: "sponsor6", label: "Global Enterprises" },
    { value: "sponsor7", label: "Tech Solutions" },
    { value: "sponsor8", label: "Marketing Pro" },
    { value: "sponsor9", label: "Digital Agency" },
    { value: "sponsor10", label: "Media Partners" },
  ];
  
  const offers = [
    { value: "offer1", label: "Premium Offer" },
    { value: "offer2", label: "Special Discount" },
    { value: "offer3", label: "Limited Time Deal" },
    { value: "offer4", label: "Exclusive Package" },
    { value: "offer5", label: "Seasonal Promotion" },
    { value: "offer6", label: "New Customer Bonus" },
    { value: "offer7", label: "Loyalty Reward" },
    { value: "offer8", label: "First-Time User" },
    { value: "offer9", label: "Holiday Special" },
    { value: "offer10", label: "Partner Promotion" },
    { value: "offer11", label: "Email Exclusive" },
    { value: "offer12", label: "Daily Deal" },
    { value: "offer13", label: "Weekend Special" },
    { value: "offer14", label: "Subscriber Benefit" },
    { value: "offer15", label: "Anniversary Offer" },
    { value: "offer16", label: "Flash Sale" },
    { value: "offer17", label: "Clearance Deal" },
    { value: "offer18", label: "VIP Package" },
    { value: "offer19", label: "Early Bird Access" },
    { value: "offer20", label: "Last Chance Offer" },
  ];
  
  const templates = [
    { value: "template1", label: "Template 1" },
    { value: "template2", label: "Template 2" },
    { value: "template3", label: "Template 3" },
    { value: "template4", label: "Professional" },
    { value: "template5", label: "Casual" },
    { value: "template6", label: "Formal" },
    { value: "template7", label: "Direct" },
    { value: "template8", label: "Question" },
    { value: "template9", label: "Announcement" },
    { value: "template10", label: "Urgent" },
  ];
  
  // Sorting function for data items
  const sortData = useCallback((key: keyof DataItem) => {
    let direction: 'asc' | 'desc' | null = 'asc';
    
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        direction = 'desc';
      } else if (sortConfig.direction === 'desc') {
        direction = null;
      }
    }
    
    setSortConfig({ key, direction });
  }, [sortConfig]);
  
  // Get sorted and paginated data
  const getSortedData = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    // Apply filters first
    let filteredItems = getFilteredData();
    
    let sortedItems = filteredItems;
    
    if (sortConfig.key && sortConfig.direction) {
      sortedItems.sort((a, b) => {
        if (a[sortConfig.key!] < b[sortConfig.key!]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key!] > b[sortConfig.key!]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    
    return sortedItems.slice(startIndex, endIndex);
  }, [dataItems, sortConfig, currentPage, itemsPerPage, geoFilter, espFilter, providerFilter, verticalFilter]);
  
  // Function to get sorting icon for headers
  const getSortIcon = useCallback((key: keyof DataItem) => {
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        return <ChevronUp className="h-3 w-3 text-primary" />;
      } else if (sortConfig.direction === 'desc') {
        return <ChevronDown className="h-3 w-3 text-primary" />;
      }
    }
    return (
      <div className="flex flex-col ml-1">
        <ChevronUp className="h-3 w-3" />
        <ChevronDown className="h-3 w-3 -mt-1" />
      </div>
    );
  }, [sortConfig]);
  
  // Get filtered data (for pagination)
  const getFilteredData = () => {
    let filteredItems = [...dataItems];
    
    if (geoFilter && geoFilter.length > 0) {
      filteredItems = filteredItems.filter(item => geoFilter.includes(item.geo));
    }
    
    if (espFilter && espFilter.length > 0) {
      filteredItems = filteredItems.filter(item => espFilter.includes(item.esp));
    }
    
    if (providerFilter && providerFilter.length > 0) {
      filteredItems = filteredItems.filter(item => providerFilter.includes(item.provider));
    }
    
    if (verticalFilter && verticalFilter.length > 0) {
      filteredItems = filteredItems.filter(item => verticalFilter.includes(item.vertical));
    }
    
    return filteredItems;
  }

  // Pagination functions
  const filteredItems = getFilteredData();
  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  // Helper functions to get unique values for filters
  const getUniqueGeos = useCallback(() => {
    const geos = dataItems.map(item => item.geo);
    return [...new Set(geos)];
  }, [dataItems]);
  
  const getUniqueEsps = useCallback(() => {
    const esps = dataItems.map(item => item.esp);
    return [...new Set(esps)];
  }, [dataItems]);
  
  const getUniqueProviders = useCallback(() => {
    const providers = dataItems.map(item => item.provider);
    return [...new Set(providers)];
  }, [dataItems]);
  
  const getUniqueVerticals = useCallback(() => {
    const verticals = dataItems.map(item => item.vertical);
    return [...new Set(verticals)];
  }, [dataItems]);
  
  // Get type columns for filtering
  const typeColumns: Array<keyof DataItem> = ['vertical', 'fresh', 'clean', 'openers', 'clickers', 'unsub', 'lead', 'otherGeos'];
  
  // Function to get human-readable column name
  const getColumnLabel = (column: keyof DataItem): string => {
    const labels: Record<keyof DataItem, string> = {
      id: 'ID',
      name: 'Name',
      geo: 'GEO',
      esp: 'ESP',
      provider: 'Provider',
      vertical: 'VERTICAL',
      fresh: 'FRESH',
      clean: 'CLEAN',
      openers: 'OPENERS',
      clickers: 'CLICKERS',
      unsub: 'UNSUB',
      lead: 'LEAD',
      otherGeos: 'OTHER GEOS'
    };
    return labels[column];
  };
  
  // Updated function to get country flag with support for all countries
  const CountryFlag = ({ country }: { country: string }) => {
    const countryCode = country.toLowerCase();
    return (
      <span 
        className="inline-block w-6 h-4 rounded-sm mr-1"
        title={getCountryName(countryCode)}
        style={{
          backgroundImage: `url(https://flagcdn.com/w20/${countryCode}.png)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          verticalAlign: 'middle',
          boxShadow: '0 0 1px rgba(0,0,0,0.1)'
        }}
      >
        <span className="sr-only">{getCountryName(countryCode)}</span>
      </span>
    );
  };

  // Add a function to clear all filters with animation
  const resetFilters = () => {
    // Skip if there are no active filters
    if (
      (!geoFilter || geoFilter.length === 0) && 
      (!espFilter || espFilter.length === 0) && 
      (!providerFilter || providerFilter.length === 0) && 
      (!verticalFilter || verticalFilter.length === 0)
    ) {
      return;
    }
    
    // Get the button element
    const resetButton = document.getElementById('reset-filters-btn');
    if (resetButton) {
      // Add animation class
      resetButton.classList.add('animate-spin', 'text-blue-500');
      
      // Reset filters after a short delay for animation effect
      setTimeout(() => {
        setGeoFilter(null);
        setEspFilter(null);
        setProviderFilter(null);
        setVerticalFilter(null);
        setCurrentPage(1); // Reset to first page when filters change
        
        // Remove animation class after the filters are reset
        setTimeout(() => {
          resetButton.classList.remove('animate-spin', 'text-blue-500');
        }, 300);
      }, 400);
    } else {
      // Fallback if button element not found
      setGeoFilter(null);
      setEspFilter(null);
      setProviderFilter(null);
      setVerticalFilter(null);
      setCurrentPage(1);
    }
  };

  const toggleFiltersVisibility = () => {
    setFiltersVisible(!filtersVisible);
    // If we're hiding the filters and there are active filters, reset them
    if (filtersVisible && (
      (geoFilter && geoFilter.length > 0) || 
      (espFilter && espFilter.length > 0) || 
      (providerFilter && providerFilter.length > 0) || 
      (verticalFilter && verticalFilter.length > 0)
    )) {
      resetFilters();
    }
  };

  // Select all cells of the chosen type
  const selectCellsByType = (value: string) => {
    // Get filtered data
    const filteredData = getFilteredData();
    
    // Select all cells of the chosen type
    const newSelectedCells = filteredData.flatMap((item: DataItem) => {
      if (value === 'all') {
        // Select all numeric fields
        return ['fresh', 'clean', 'openers', 'clickers', 'unsub', 'lead', 'otherGeos']
          .map(field => ({
            itemId: item.id,
            field: field as keyof DataItem,
            value: item[field as keyof DataItem]
          }));
      } else if (value === 'fresh') {
        // Just fresh
        return [{ itemId: item.id, field: 'fresh' as keyof DataItem, value: item.fresh }];
      } else if (value === 'clean') {
        // Just clean
        return [{ itemId: item.id, field: 'clean' as keyof DataItem, value: item.clean }];
      } else if (value === 'openers') {
        // Just openers
        return [{ itemId: item.id, field: 'openers' as keyof DataItem, value: item.openers }];
      } else if (value === 'clickers') {
        // Just clickers
        return [{ itemId: item.id, field: 'clickers' as keyof DataItem, value: item.clickers }];
      } else if (value === 'unsub') {
        // Just unsub
        return [{ itemId: item.id, field: 'unsub' as keyof DataItem, value: item.unsub }];
      } else if (value === 'lead') {
        // Just lead
        return [{ itemId: item.id, field: 'lead' as keyof DataItem, value: item.lead }];
      } else if (value === 'otherGeos') {
        // Just other geos
        return [{ itemId: item.id, field: 'otherGeos' as keyof DataItem, value: item.otherGeos }];
      }
      return [];
    });
  };

  // State for selected types
  const [selectedTypeValues, setSelectedTypeValues] = useState<string[] | null>(null);
  const [finishValue, setFinishValue] = useState<number>(0);
  
  useEffect(() => {
    // Add a style tag to hide scrollbars but maintain scrolling functionality
    const style = document.createElement('style');
    style.textContent = `
      /* Ensure all dropdown options are visible */
      .max-h-\\[200px\\] {
        max-height: 350px !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Update finishValue when selectedCells changes
  useEffect(() => {
    const sum = selectedCells.reduce((total, cell) => {
      return total + (typeof cell.value === 'number' ? cell.value : 0);
    }, 0);
    setFinishValue(sum);
  }, [selectedCells]);

  // Test Results data
  const testResults = [
    {
      id: 1,
      server: '2xx.xx.xx.19',
      interface: '192.168.1.1',
      returnPath: 'bounce.mx1.tx1.BannerDnsSrvPlatform-01.lightbase.ru',
      receivedEmail: '<EMAIL>',
      testStatus: 'Success',
    },
    {
      id: 2,
      server: '2xx.xx.xx.37',
      interface: '192.168.1.2',
      returnPath: 'bounce.mx1.tx1.BannerDnsSrvPlatform-01.lightbase.ru',
      receivedEmail: '<EMAIL>',
      testStatus: 'Success',
    },
  ];

  // State for test results sorting
  const [testSortConfig, setTestSortConfig] = useState<{
    key: keyof typeof testResults[0] | null;
    direction: 'asc' | 'desc' | null;
  }>({
    key: null,
    direction: null
  });
  
  // Function to handle test results sorting
  const sortTestResults = useCallback((key: keyof typeof testResults[0]) => {
    let direction: 'asc' | 'desc' | null = 'asc';
    
    if (testSortConfig.key === key) {
      if (testSortConfig.direction === 'asc') {
        direction = 'desc';
      } else if (testSortConfig.direction === 'desc') {
        direction = null;
      }
    }
    
    setTestSortConfig({ key, direction });
  }, [testSortConfig]);
  
  // Function to get test sorting icon
  const getTestSortIcon = useCallback((key: keyof typeof testResults[0]) => {
    if (testSortConfig.key === key) {
      if (testSortConfig.direction === 'asc') {
        return <ChevronUp className="h-3 w-3 text-primary" />;
      } else if (testSortConfig.direction === 'desc') {
        return <ChevronDown className="h-3 w-3 text-primary" />;
      }
    }
    return (
      <div className="flex flex-col ml-1 opacity-40 group-hover:opacity-100">
        <ChevronUp className="h-3 w-3" />
        <ChevronDown className="h-3 w-3 -mt-1" />
      </div>
    );
  }, [testSortConfig]);
  
  // Get sorted test results
  const getSortedTestResults = useCallback(() => {
    let sortedResults = [...testResults];
    
    if (testSortConfig.key && testSortConfig.direction) {
      sortedResults.sort((a, b) => {
        const aValue = a[testSortConfig.key!];
        const bValue = b[testSortConfig.key!];
        
        if (aValue < bValue) {
          return testSortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return testSortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    
    return sortedResults;
  }, [testResults, testSortConfig]);

  const totalTests = 2;
  const completedTests = 3;

  // Geo filter
  const existingGeos = useMemo(() => {
    const geos = Array.from(new Set(dataItems.map(item => item.geo)));
    return geos.map(geo => ({
      value: geo,
      label: (
        <div className="flex items-center gap-2">
          <CountryFlag country={geo} />
          <span>{geo.toUpperCase()}</span>
        </div>
      ),
      searchableText: `${geo.toUpperCase()} ${getCountryName(geo)}`
    }));
  }, [dataItems]);

  const otherCountries = useMemo(() => {
    const usedGeos = new Set(existingGeos.map(geo => geo.value));
    return countries
      .filter(country => !usedGeos.has(country.code))
      .map(country => ({
        value: country.code,
        label: (
          <div className="flex items-center gap-2">
            <CountryFlag country={country.code} />
            <span>{country.code.toUpperCase()}</span>
          </div>
        ),
        searchableText: `${country.code.toUpperCase()} ${country.name}`
      }));
  }, [existingGeos, countries]);

  // Function to reset header to default template
  const resetHeaderToDefault = () => {
    const defaultHeaderTemplate = `MIME-Version: 1.0
Message-Id: <[a_7].[n_5].[n_3].[a_3]@[domain]>
From: [a_5] <[a_7]@[domain]>
Subject: [ip] [server] [an_5]
Reply-To: reply_to@[domain]
To: [email]
Content-Transfer-Encoding: [content_transfer_encoding]
Content-Type: [content_type]; charset=[charset]
Date: [mail_date]`;

    updateHeaderContent(defaultHeaderTemplate);
  };

  // Function to randomize header lines without changing their content
  const randomizeHeaderLines = () => {
    if (!activeHeader.content) return;
    
    // Split content into lines
    const lines = activeHeader.content.split('\n');
    
    // Fisher-Yates shuffle algorithm
    for (let i = lines.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [lines[i], lines[j]] = [lines[j], lines[i]];
    }
    
    // Join lines back together
    const randomizedContent = lines.join('\n');
    
    // Update the header content
    updateHeaderContent(randomizedContent);
  };

  const openPreviewInNewTab = () => {
    if (previewContent) {
      const newTab = window.open();
      if (newTab) {
        newTab.document.write(previewContent);
      }
    }
  };

  // Function to handle device view toggle
  const toggleDeviceView = (view: 'pc' | 'phone' | 'tablet') => {
    setDeviceView(view);
  };

  return (
    <ContentLayout title="Send Page">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/production">Production</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Send Page</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      
      {/* Delivery Servers Card */}
      <Card className="mt-6 overflow-hidden">
        <CardHeader 
          className="flex flex-row items-center justify-between py-3 pl-4 relative"
        >
          <div className="flex items-center gap-2 flex-grow cursor-pointer"
            onClick={() => {/* Toggle function can be added here */}}
          >
            <CardTitle>Delivery Servers</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1.5">
              <Label htmlFor="server-type" className="text-sm whitespace-nowrap">Type:</Label>
              <Select defaultValue="all">
                <SelectTrigger id="server-type" className="w-[100px] h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="original">Original</SelectItem>
                  <SelectItem value="vmta">Vmta</SelectItem>
                  <SelectItem value="smtp">Smtp</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-1.5">
              <Label htmlFor="ip-version" className="text-sm whitespace-nowrap">IP:</Label>
              <Select defaultValue="ipv4">
                <SelectTrigger id="ip-version" className="w-[90px] h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ipv4">IPv4</SelectItem>
                  <SelectItem value="ipv6">IPv6</SelectItem>
                  <SelectItem value="all">All</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="default" size="sm" className={`${colorPalette.info.focusBg} ${colorPalette.info.focusText}`}>AUTO</Button>
            <Button variant="default" size="sm" className={`${colorPalette.violet.focusBg} ${colorPalette.violet.focusText}`}>SELECT</Button>
            <Button className={`${colorPalette.primary.focusBg} ${colorPalette.primary.focusText} shadow-sm`} size="sm">PMTA QUEUES</Button>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="card-container flex gap-4" style={{ isolation: 'isolate' }}>
            {/* Delivery Servers Section */}
            <div className="w-1/2 space-y-3">
              <h2 className="text-md font-medium">Delivery Servers</h2>
              <div className="flex gap-2">
                {/* Available List */}
                <div className="w-[45%] border border-border rounded-md">
                  <div className="p-2 border-b border-border">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium">Available ({availableServers.length})</p>
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Select all items');
                            selectAllServers();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Select all"
                        >
                          <CheckSquare className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Deselect all items');
                            deselectAllServers();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Deselect all"
                        >
                          <Square className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            // Refresh functionality would go here
                            console.log('Refreshing available servers...');
                            setIsServersRefreshing(true);
                            
                            // Simulate refresh with a timeout
                            setTimeout(() => {
                              setIsServersRefreshing(false);
                            }, 1500);
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Refresh available servers"
                        >
                          <RefreshCw className={`h-3.5 w-3.5 ${colorPalette.secondary.text} ${isServersRefreshing ? 'animate-spin' : ''}`} />
                        </Button>
                      </div>
                    </div>
                    <Input 
                      type="search" 
                      placeholder="Search..." 
                      className="h-8"
                    />
                  </div>
                  <div className="h-[200px] overflow-auto p-2">
                    {availableServers.map((server) => (
                      <div 
                        key={`avail-${server.id}`}
                        className={`p-2 text-sm rounded cursor-pointer hover:bg-muted flex items-center gap-2 ${
                          server.isSelected ? `${colorPalette.secondary.light}` : ''
                        }`}
                        onClick={() => toggleServerSelection(server.id)}
                      >
                        <div 
                          className={`w-4 h-4 border rounded flex-shrink-0 flex items-center justify-center ${
                            server.isSelected ? `${colorPalette.secondary.medium} ${colorPalette.secondary.border}` : 'border-border'
                          }`}
                        >
                          {server.isSelected && <div className={`w-2 h-2 ${colorPalette.secondary.focusBg} rounded-sm`}></div>}
                        </div>
                        <span className={server.name.includes("test example") ? `font-medium ${colorPalette.secondary.text}` : ""}>
                          {server.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Transfer Buttons */}
                <div className="flex flex-col justify-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={transferAllToRight}
                    disabled={availableServers.length === 0}
                    className={`${colorPalette.primary.text} ${colorPalette.primary.border} ${colorPalette.primary.hover}`}
                  >
                    &gt;&gt;
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={transferSelectedToRight}
                    disabled={selectedAvailableCount === 0}
                    className={`${colorPalette.primary.text} ${colorPalette.primary.border} ${colorPalette.primary.hover}`}
                  >
                    &gt;
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={transferSelectedToLeft}
                    disabled={selectedFromSelectedCount === 0}
                    className={`${colorPalette.warning.text} ${colorPalette.warning.border} ${colorPalette.warning.hover}`}
                  >
                    &lt;
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={transferAllToLeft}
                    disabled={selectedServers.length === 0}
                    className={`${colorPalette.warning.text} ${colorPalette.warning.border} ${colorPalette.warning.hover}`}
                  >
                    &lt;&lt;
                  </Button>
                </div>
                
                {/* Selected List */}
                <div className="w-[45%] border border-border rounded-md">
                  <div className="p-2 border-b border-border">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium">Selected ({selectedServers.length})</p>
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Select all selected servers');
                            selectAllSelectedServers();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Select all"
                        >
                          <CheckSquare className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Deselect all selected servers');
                            deselectAllSelectedServers();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Deselect all"
                        >
                          <Square className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                      </div>
                    </div>
                    <Input 
                      type="search" 
                      placeholder="Search..." 
                      className="h-8"
                    />
                  </div>
                  <div className="h-[200px] overflow-auto p-2">
                    {selectedServers.map((server) => (
                      <div 
                        key={`sel-${server.id}`}
                        className={`p-2 text-sm rounded cursor-pointer hover:bg-muted flex items-center gap-2 ${
                          server.isSelected ? `${colorPalette.secondary.light}` : ''
                        }`}
                        onClick={() => toggleSelectedServerSelection(server.id)}
                      >
                        <div 
                          className={`w-4 h-4 border rounded flex-shrink-0 flex items-center justify-center ${
                            server.isSelected ? `${colorPalette.secondary.medium} ${colorPalette.secondary.border}` : 'border-border'
                          }`}
                        >
                          {server.isSelected && <div className={`w-2 h-2 ${colorPalette.secondary.focusBg} rounded-sm`}></div>}
                        </div>
                        <span>{server.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Servers Interfaces Section */}
            <div className="w-1/2 space-y-3">
              <h2 className="text-md font-medium">Servers Interfaces</h2>
              <div className="flex gap-2">
                {/* Available List */}
                <div className="w-[45%] border border-border rounded-md">
                  <div className="p-2 border-b border-border">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium">Available ({availableInterfaces.length})</p>
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Select all interfaces');
                            selectAllInterfaces();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Select all"
                        >
                          <CheckSquare className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Deselect all interfaces');
                            deselectAllInterfaces();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Deselect all"
                        >
                          <Square className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            // Refresh functionality would go here
                            console.log('Refreshing available interfaces...');
                            setIsInterfacesRefreshing(true);
                            
                            // Simulate refresh with a timeout
                            setTimeout(() => {
                              setIsInterfacesRefreshing(false);
                            }, 1500);
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Refresh available interfaces"
                        >
                          <RefreshCw className={`h-3.5 w-3.5 ${colorPalette.secondary.text} ${isInterfacesRefreshing ? 'animate-spin' : ''}`} />
                        </Button>
                      </div>
                    </div>
                    <Input 
                      type="search" 
                      placeholder="Search..." 
                      className="h-8"
                    />
                  </div>
                  <div className="h-[200px] overflow-auto p-2">
                    {availableInterfaces.length > 0 ? (
                      availableInterfaces.map((iface) => (
                        <div 
                          key={`avail-int-${iface.id}`}
                          className={`p-2 text-sm rounded cursor-pointer hover:bg-muted flex items-center gap-2 ${
                            iface.isSelected ? `${colorPalette.secondary.light}` : ''
                          }`}
                          onClick={() => {
                            // Toggle interface selection
                            setAvailableInterfaces(availableInterfaces.map(item => 
                              item.id === iface.id ? { ...item, isSelected: !item.isSelected } : item
                            ));
                          }}
                        >
                          <div 
                            className={`w-4 h-4 border rounded flex-shrink-0 flex items-center justify-center ${
                              iface.isSelected ? `${colorPalette.secondary.medium} ${colorPalette.secondary.border}` : 'border-border'
                            }`}
                          >
                            {iface.isSelected && <div className={`w-2 h-2 ${colorPalette.secondary.focusBg} rounded-sm`}></div>}
                          </div>
                          <span className={iface.name.includes("test example") ? `font-medium ${colorPalette.secondary.text}` : ""}>
                            {iface.name}
                          </span>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-sm">No items found</p>
                    )}
                  </div>
                </div>
                
                {/* Transfer Buttons */}
                <div className="flex flex-col justify-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={transferAllInterfacesToRight}
                    disabled={availableInterfaces.length === 0}
                    className={`${colorPalette.primary.text} ${colorPalette.primary.border} ${colorPalette.primary.hover}`}
                  >
                    &gt;&gt;
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={transferSelectedInterfacesToRight}
                    disabled={selectedAvailableInterfacesCount === 0}
                    className={`${colorPalette.primary.text} ${colorPalette.primary.border} ${colorPalette.primary.hover}`}
                  >
                    &gt;
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={transferSelectedInterfacesToLeft}
                    disabled={selectedFromSelectedInterfacesCount === 0}
                    className={`${colorPalette.warning.text} ${colorPalette.warning.border} ${colorPalette.warning.hover}`}
                  >
                    &lt;
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={transferAllInterfacesToLeft}
                    disabled={selectedInterfaces.length === 0}
                    className={`${colorPalette.warning.text} ${colorPalette.warning.border} ${colorPalette.warning.hover}`}
                  >
                    &lt;&lt;
                  </Button>
                </div>
                
                {/* Selected List */}
                <div className="w-[45%] border border-border rounded-md">
                  <div className="p-2 border-b border-border">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium">Selected ({selectedInterfaces.length})</p>
                      <div className="flex items-center gap-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Select all selected interfaces');
                            selectAllSelectedInterfaces();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Select all"
                        >
                          <CheckSquare className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => {
                            console.log('Deselect all selected interfaces');
                            deselectAllSelectedInterfaces();
                          }}
                          className={`h-6 w-6 ${colorPalette.secondary.hover}`}
                          title="Deselect all"
                        >
                          <Square className={`h-3.5 w-3.5 ${colorPalette.secondary.text}`} />
                        </Button>
                      </div>
                    </div>
                    <Input 
                      type="search" 
                      placeholder="Search..." 
                      className="h-8"
                    />
                  </div>
                  <div className="h-[200px] overflow-auto p-2">
                    {selectedInterfaces.map((iface) => (
                      <div 
                        key={`sel-int-${iface.id}`}
                        className={`p-2 text-sm rounded cursor-pointer hover:bg-muted flex items-center gap-2 ${
                          iface.isSelected ? `${colorPalette.secondary.light}` : ''
                        }`}
                        onClick={() => toggleInterfaceSelection(iface.id)}
                      >
                        <div 
                          className={`w-4 h-4 border rounded flex-shrink-0 flex items-center justify-center ${
                            iface.isSelected ? `${colorPalette.secondary.medium} ${colorPalette.secondary.border}` : 'border-border'
                          }`}
                        >
                          {iface.isSelected && <div className={`w-2 h-2 ${colorPalette.secondary.focusBg} rounded-sm`}></div>}
                        </div>
                        <span>{iface.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Email Headers Card */}
      <Card className="mt-6 overflow-hidden relative z-0">
        <CardHeader 
          className="flex flex-row items-center justify-between py-3 pl-4 relative z-10"
        >
          <div className="flex items-center gap-2">
            <CardTitle>Email Headers</CardTitle>
          </div>
        </CardHeader>
          <CardContent>
          <div className="card-container flex gap-4" style={{ isolation: 'isolate' }}>
            {/* Left Column with Stacked Cards */}
            <div className="w-1/2 space-y-4">
              {/* Top Card - Header Editor */}
              <Card className="shadow-md relative z-10 overflow-hidden">
              <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                <div className="flex items-center gap-1.5 z-10 flex-shrink-0">
                  <CardTitle className="text-base font-medium drop-shadow-sm">Header Editor</CardTitle>
                      </div>
                      
                <div className="flex items-center gap-1.5 z-10">
                  <div className="flex items-center mr-2">
                    <Label htmlFor="header-rotation" className="text-xs mr-1.5">Rotation:</Label>
                    <Input 
                      id="header-rotation" 
                      className="h-7 w-20 text-xs"
                      defaultValue="1" 
                      type="number"
                      min="1"
                    />
                  </div>
                  
                  <Select 
                    value={activeHeader.template} 
                    onValueChange={updateHeaderTemplate}
                  >
                    <SelectTrigger id="header-template" className="h-7 text-xs">
                      <SelectValue placeholder="Select header" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default Header</SelectItem>
                      <SelectItem value="minimal">Minimal Header</SelectItem>
                      <SelectItem value="professional">Professional Template</SelectItem>
                      <SelectItem value="custom">Custom Header</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  {/* New plus icon button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 flex-shrink-0 ml-1"
                    title="Add New"
                  >
                    <Plus className="h-3.5 w-3.5" />
                  </Button>
                  
                  {/* Reset header icon button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 flex-shrink-0 ml-1"
                    title="Reset Header"
                    onClick={resetHeaderToDefault}
                  >
                    <RotateCcw className="h-3.5 w-3.5" />
                  </Button>
                  
                  {/* Randomize header icon button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 flex-shrink-0 ml-1"
                    title="Randomize Header"
                    onClick={randomizeHeaderLines}
                  >
                    <Shuffle className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </CardHeader>
                        
              {/* Tabs for headers with scroll indicators */}
              <div className="relative">
                {/* Left scroll indicator */}
                {showLeftScroll && (
                  <button 
                    className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background dark:bg-gray-800 rounded-full shadow-md p-0.5 border border-border"
                    onClick={() => scrollTabs('left')}
                    aria-label="Scroll left"
                  >
                    <ChevronLeft className="h-4 w-4 text-muted-foreground" />
                  </button>
                )}
                
                {/* Right scroll indicator */}
                {showRightScroll && (
                  <button 
                    className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background dark:bg-gray-800 rounded-full shadow-md p-0.5 border border-border"
                    onClick={() => scrollTabs('right')}
                    aria-label="Scroll right"
                  >
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  </button>
                )}
                
                {/* Scrollable tabs container */}
                <div 
                  className="relative flex items-center mb-1 overflow-x-auto no-scrollbar"
                  ref={tabsContainerRef}
                  onScroll={checkScrollIndicators}
                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                >
                <div className="flex border-b border-border w-full">
                  {headers.map(header => (
                    <div 
                      key={header.id}
                      data-header-id={header.id}
                      className={`relative flex items-center py-2 px-3 text-sm cursor-pointer transition-colors
                        ${activeHeaderId === header.id 
                          ? 'text-primary font-medium border-b-2 border-primary' 
                          : 'text-foreground hover:text-foreground hover:bg-muted/50'} 
                        ${headers.length > 8 ? 'flex-shrink-0' : ''}`}
                      onClick={() => setActiveHeaderId(header.id)}
                    >
                      <span className="mr-2 truncate max-w-[100px]" title={header.name}>
                        {header.name}
                      </span>
                      {headers.length > 1 && (
                        <X 
                          className="h-3.5 w-3.5 text-muted-foreground hover:text-foreground" 
                          onClick={(e) => {
                            e.stopPropagation();
                            removeHeader(header.id);
                          }}
                        />
                      )}
                          </div>
                  ))}
                  
                  {/* Add New Header Button - icon only */}
                  <div className="flex-grow flex justify-end">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-[34px] w-[34px] flex items-center justify-center text-muted-foreground hover:text-foreground"
                      onClick={addNewHeader}
                      title="Add New Header"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                        </div>
                        </div>
                      </div>
                      
              <CardContent className="p-4">
                <div className="flex rounded-md border border-input overflow-hidden">
                  <div className="relative flex-grow">
                    <Textarea 
                      id="email-header" 
                        className="min-h-[250px] font-mono text-sm rounded-none border-none resize-none focus-visible:ring-0 focus-visible:ring-offset-0 leading-[1.6rem] tracking-wide selection:bg-blue-200 dark:selection:bg-blue-800/50 selection:shadow-md"
                      value={activeHeader.content}
                      onChange={(e) => updateHeaderContent(e.target.value)}
                      spellCheck={false}
                            />
                    {/* WideArea Mode toggle button for Header Editor */}
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute bottom-3 right-3 bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 h-8 w-8 rounded-full shadow-md hover:shadow-lg border border-gray-200 dark:border-gray-700"
                      onClick={() => setIsHeaderWideAreaMode(true)}
                      title="Toggle WideArea Mode"
                    >
                      <Maximize2 className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </Button>
                          </div>
                        </div>
              </CardContent>
            </Card>

              {/* Test Emails Card */}
              <Card className="shadow-md relative z-10">
                <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                  <div className="flex items-center gap-1.5 z-10 flex-shrink-0">
                    <CardTitle className="text-base font-medium drop-shadow-sm">Test Emails</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="test-emails">Email Addresses:</Label>
                      <Textarea 
                        id="test-emails" 
                        placeholder="Enter test email addresses (one per line)"
                        className="min-h-[100px]"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="test-every">Test After:</Label>
                      <div className="flex items-center space-x-2">
                        <Input 
                          id="test-every" 
                          placeholder="Enter number"
                          type="number"
                          min="1"
                          defaultValue="100"
                          className="shadow-sm w-24"
                        />
                        <span className="text-sm text-muted-foreground">emails</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Bottom Card - Sending Speed */}
              <Card className="shadow-md hover:shadow-lg transition-shadow relative z-10 overflow-hidden mt-4">
                <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                  <div className="flex items-center gap-1.5 z-10 flex-shrink-0">
                    <CardTitle className="text-base font-medium drop-shadow-sm">Sending Speed</CardTitle>
                            </div>
                </CardHeader>
                
                <CardContent className="p-4">
                  <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="send-type">Send Type:</Label>
                          <Select>
                            <SelectTrigger id="send-type" className="w-full">
                              <SelectValue placeholder="Select send type..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="type1">Regular</SelectItem>
                              <SelectItem value="type2">Batch</SelectItem>
                              <SelectItem value="type3">Sequential</SelectItem>
                              <SelectItem value="type4">Priority</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="change-method">Change Method:</Label>
                          <Select>
                            <SelectTrigger id="change-method" className="w-full">
                              <SelectValue placeholder="Select change method..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="method1">Round Robin</SelectItem>
                              <SelectItem value="method2">Sequential</SelectItem>
                              <SelectItem value="method3">Random</SelectItem>
                              <SelectItem value="method4">Load Balanced</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="change-interface">Change Interface Every:</Label>
                          <Input 
                            id="change-interface" 
                            placeholder="Enter value" 
                            type="number"
                            min="1"
                            defaultValue="1"
                          />
                        </div>
                        
                        <div className="space-y-2">
                        <Label htmlFor="sending-speed">Speed Rate:</Label>
                          <div className="flex items-center space-x-2">
                            <Input 
                              id="sending-speed" 
                              placeholder="Enter speed"
                              type="number"
                              min="1"
                              defaultValue="10"
                              className="shadow-sm"
                            />
                            <Select defaultValue="minute">
                              <SelectTrigger className="w-[110px]">
                                <SelectValue placeholder="Select unit" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="second">Second</SelectItem>
                                <SelectItem value="minute">Minute</SelectItem>
                                <SelectItem value="hour">Hour</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                  </div>
                </CardContent>
              </Card>
                        </div>
                        
            {/* Right Column with Stacked Cards */}
            <div className="w-1/2 flex flex-col space-y-4">
              {/* Sponsor & Offer Card */}
              <Card className="shadow-md hover:shadow-lg transition-shadow relative z-10 overflow-hidden">
                <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                  <div className="flex items-center gap-1.5 z-10">
                    <CardTitle className="text-base font-medium drop-shadow-sm">Sponsor & Offer</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {/* Sponsor and Offer searchable selects */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sponsor">Sponsor:</Label>
                        <SearchableSelect
                          id="sponsor"
                          value=""
                          onValueChange={(value) => {/* Handle sponsor change */}}
                          placeholder="Select sponsor..."
                          triggerClassName="w-full"
                          searchPlaceholder="Search sponsors..."
                          items={[
                            { value: "sponsor1", label: "Sponsor 1" },
                            { value: "sponsor2", label: "Sponsor 2" },
                            { value: "sponsor3", label: "Sponsor 3" },
                            { value: "sponsor4", label: "Sponsor 4" },
                          ]}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="offer">Offer:</Label>
                        <SearchableSelect
                          id="offer"
                          value=""
                          onValueChange={(value) => {/* Handle offer change */}}
                          placeholder="Select offer..."
                          triggerClassName="w-full"
                          searchPlaceholder="Search offers..."
                          items={[
                            { value: "offer1", label: "Offer 1" },
                            { value: "offer2", label: "Offer 2" },
                            { value: "offer3", label: "Offer 3" },
                            { value: "offer4", label: "Offer 4" },
                          ]}
                        />
                      </div>
                    </div>
                    
                    {/* From Name and Encoding in the same row */}
                    <div className="grid grid-cols-4 gap-4">
                      <div className="space-y-2 col-span-3">
                        <Label htmlFor="from-name" className="text-base font-medium">From Name:</Label>
                        <Input 
                          id="from-name" 
                          placeholder="Enter sender name"
                          defaultValue=""
                          className="text-base h-10" 
                        />
                      </div>
                      
                      <div className="space-y-2 col-span-1">
                        <Label htmlFor="from-name-encoding">Encoding:</Label>
                        <Select>
                          <SelectTrigger id="from-name-encoding" className="w-[120px]">
                            <SelectValue placeholder="Select..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="base64">Base64</SelectItem>
                            <SelectItem value="quoted-printable">Quoted Printable</SelectItem>
                            <SelectItem value="url">URL Encoded</SelectItem>
                            <SelectItem value="q">Q-Encoding</SelectItem>
                            <SelectItem value="b">B-Encoding</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    {/* Subject and Encoding in the same row */}
                    <div className="grid grid-cols-4 gap-4">
                      <div className="space-y-2 col-span-3">
                        <Label htmlFor="subject" className="text-base font-medium">Subject:</Label>
                        <Input 
                          id="subject" 
                          placeholder="Enter email subject"
                          defaultValue=""
                          className="text-base h-10" 
                        />
                      </div>
                      
                      <div className="space-y-2 col-span-1">
                        <Label htmlFor="subject-field-encoding">Encoding:</Label>
                        <Select>
                          <SelectTrigger id="subject-field-encoding" className="w-[120px]">
                            <SelectValue placeholder="Select..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="base64">Base64</SelectItem>
                            <SelectItem value="quoted-printable">Quoted Printable</SelectItem>
                            <SelectItem value="url">URL Encoded</SelectItem>
                            <SelectItem value="q">Q-Encoding</SelectItem>
                            <SelectItem value="b">B-Encoding</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Configuration Card */}
              <Card className="shadow-md hover:shadow-lg transition-shadow relative z-10 overflow-hidden">
                <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                  <div className="flex items-center gap-1.5 z-10">
                    <CardTitle className="text-base font-medium drop-shadow-sm">Configuration</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  {/* Added form selects for email configuration */}
                  <div className="space-y-4">
                    {/* Content Type, Charset, and Transfer Encoding in a row */}
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="content-type">Content Type:</Label>
                        <Select>
                          <SelectTrigger id="content-type" className="w-full">
                            <SelectValue placeholder="Select content t..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text/plain">text/plain</SelectItem>
                            <SelectItem value="text/html">text/html</SelectItem>
                            <SelectItem value="multipart/alternative">multipart/alternative</SelectItem>
                            <SelectItem value="multipart/mixed">multipart/mixed</SelectItem>
                            <SelectItem value="multipart/related">multipart/related</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="charset">Charset:</Label>
                        <Select>
                          <SelectTrigger id="charset" className="w-full">
                            <SelectValue placeholder="Select charset..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="UTF-8">UTF-8</SelectItem>
                            <SelectItem value="ISO-8859-1">ISO-8859-1</SelectItem>
                            <SelectItem value="ASCII">ASCII</SelectItem>
                            <SelectItem value="UTF-16">UTF-16</SelectItem>
                            <SelectItem value="Windows-1252">Windows-1252</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="transfer-encoding">Transf Encoding:</Label>
                        <Select>
                          <SelectTrigger id="transfer-encoding" className="w-full">
                            <SelectValue placeholder="Select encoding..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="7bit">7bit</SelectItem>
                            <SelectItem value="8bit">8bit</SelectItem>
                            <SelectItem value="binary">binary</SelectItem>
                            <SelectItem value="quoted-printable">quoted-printable</SelectItem>
                            <SelectItem value="base64">base64</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    {/* Return Path and X-mailer fields at the bottom */}
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div className="space-y-2">
                        <Label htmlFor="return-path">Return Path:</Label>
                        <Input 
                          id="return-path" 
                          placeholder="Enter return path"
                          defaultValue="bounce@[domain]" 
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="x-mailer">X-mailer:</Label>
                        <Select>
                          <SelectTrigger id="x-mailer" className="w-full">
                            <SelectValue placeholder="Select client..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="outlook">Microsoft Outlook</SelectItem>
                            <SelectItem value="apple">Apple Mail</SelectItem>
                            <SelectItem value="gmail">Gmail</SelectItem>
                            <SelectItem value="yahoo">Yahoo Mail</SelectItem>
                            <SelectItem value="thunderbird">Mozilla Thunderbird</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            </div>
          </CardContent>
      </Card>
      
      {/* Creative Card */}
      <Card className="mt-6 overflow-hidden relative z-0">
        <CardHeader 
          className="flex flex-row items-center justify-between py-3 pl-4 relative z-10"
        >
          <div className="flex items-center gap-2 flex-grow overflow-hidden">
            <CardTitle>Creative</CardTitle>
            
            {/* Creative Tabs with Horizontal Scrolling */}
            <div className="relative flex-grow overflow-hidden ml-4">
              {/* Left scroll indicator (now scrolls right) */}
              {showCreativesLeftScroll && (
                <button 
                  className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background dark:bg-gray-800 rounded-full shadow-md p-0.5 border border-border"
                  onClick={() => scrollCreativeTabs('left')}
                  aria-label="Scroll right"
                >
                  <ChevronLeft className="h-4 w-4 text-muted-foreground" />
                </button>
              )}
              
              {/* Right scroll indicator (now scrolls left) */}
              {showCreativesRightScroll && (
                <button 
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background dark:bg-gray-800 rounded-full shadow-md p-0.5 border border-border"
                  onClick={() => scrollCreativeTabs('right')}
                  aria-label="Scroll left"
                >
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                </button>
              )}
              
              {/* Creative tabs container */}
              <div 
                ref={creativesTabsContainerRef} 
                className="relative flex items-center mb-1 overflow-x-auto no-scrollbar"
                onScroll={checkCreativesScrollIndicators}
                style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              >
                <div className="flex border-b border-border w-full justify-end">
                  {creatives.map(creative => (
                    <div
                      key={creative.id}
                      data-creative-id={creative.id}
                      className={`relative flex items-center py-2 px-3 text-sm cursor-pointer transition-colors
                        ${activeCreativeId === creative.id 
                          ? 'text-primary font-medium border-b-2 border-primary' 
                          : 'text-foreground hover:text-foreground hover:bg-muted/50'} 
                        ${creatives.length > 4 ? 'flex-shrink-0' : ''}`}
                      onClick={() => setActiveCreativeId(creative.id)}
                    >
                      <span className="mr-2 truncate max-w-[100px]" title={creative.name}>
                        {creative.name}
                      </span>
                      {creatives.length > 1 && (
                        <X 
                          className="h-3.5 w-3.5 text-muted-foreground hover:text-foreground" 
                          onClick={(e) => {
                            e.stopPropagation();
                            removeCreative(creative.id);
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Add Creative Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={addNewCreative}
            title="Add New Creative"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent>
          <div className="card-container flex gap-4" style={{ isolation: 'isolate' }}>
            {/* HTML Creative Card */}
            <Card className="w-1/2 shadow-md hover:shadow-lg transition-shadow relative z-10 overflow-hidden">
              <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                <div className="flex items-center gap-1.5 z-10 flex-shrink-0">
                  <CardTitle className="text-base font-medium drop-shadow-sm">HTML Editor</CardTitle>
                </div>
                
                {/* Search box */}
                <div className="flex items-center relative z-10 mx-auto pl-4 flex-grow max-w-md">
                  <div className="relative w-full flex items-center">
                    <div title="Click to focus search (or press Ctrl+F)">
                      <Search 
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground cursor-pointer hover:text-foreground" 
                        onClick={focusSearchInput}
                      />
                    </div>
                    <Input 
                      type="text"
                      placeholder="Search in HTML... (Enter)"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyDown={handleSearchKeyDown}
                      ref={searchInputRef}
                      className={`pl-8 h-7 text-xs w-full truncate ${isSearchActive ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : 'bg-background'}`}
                    />
                    {searchTerm && (
                      <div title="Clear search (Esc)">
                        <X 
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground cursor-pointer hover:text-foreground"
                          onClick={() => {
                            setSearchTerm('');
                            setIsSearchActive(false);
                            setSearchResults({count: 0, current: 0});
                            setLastCaretPosition(null);
                            focusSearchInput();
                          }}
                        />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-1 ml-1 flex-shrink-0">
                    {isSearchActive && searchResults.count > 0 && (
                      <div className="text-xs text-blue-500 dark:text-blue-400 mx-1 whitespace-nowrap">
                        {searchResults.current + 1}/{searchResults.count}
                      </div>
                    )}
                    
                    {isSearchActive && searchResults.count > 0 && (
                      <>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={handlePrevResult}
                          className="h-6 w-6"
                          title="Previous match (Shift+Enter)"
                          disabled={searchResults.count <= 1}
                        >
                          <ChevronLeft className="h-3 w-3" />
                        </Button>
                        
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={handleNextResult}
                          className="h-6 w-6"
                          title="Next match (Enter)"
                          disabled={searchResults.count <= 1}
                        >
                          <ChevronRight className="h-3 w-3" />
                        </Button>
                      </>
                    )}
                    
                    <Button 
                      variant={isSearchActive ? "default" : "ghost"} 
                      size="icon"
                      onClick={() => {
                        handleSearch();
                        focusSearchInput();
                      }}
                      className={`h-6 w-6 ${isSearchActive ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'text-muted-foreground hover:text-foreground'}`}
                      title="Find (Enter)"
                    >
                      <Search className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center gap-1.5 z-10 flex-shrink-0">
                  <div className="text-xs text-muted-foreground border border-border rounded px-1.5 py-0.5">
                    <span title="Total Lines">{lineCount} lines</span>
                  </div>
                  <div className="text-xs text-muted-foreground border border-border rounded px-1.5 py-0.5">
                    <span title="HTML Content Size">{htmlSize}</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-6 text-xs py-0 px-2 text-red-500 dark:text-red-400 border-red-200 dark:border-red-900/60 hover:bg-red-50 dark:hover:bg-red-950/30 hover:text-red-600 dark:hover:text-red-300 relative z-20"
                    onClick={handleClearHtml}
                  >
                    Clear
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <div className="flex rounded-md border border-input overflow-hidden">
                  <div 
                    id="line-numbers" 
                    className="bg-muted/50 text-muted-foreground font-mono text-xs p-1 text-right min-w-[2.5rem] overflow-hidden"
                    style={{ height: '500px' }}
                  >
                    {renderLineNumbers()}
                  </div>
                  <div className="relative flex-grow">
                    <Textarea 
                      id="html-creative" 
                      ref={textAreaRef}
                      className="min-h-[500px] font-mono text-sm rounded-none border-none resize-none focus-visible:ring-0 focus-visible:ring-offset-0 leading-[1.5rem] selection:bg-blue-200 dark:selection:bg-blue-800/50 selection:shadow-md"
                      placeholder="Enter your HTML creative content here..."
                      value={activeCreative.content}
                      onChange={(e) => {
                        // Direct state update for content with minimal overhead
                        const content = e.target.value;
                        updateCreativeContent(content);
                        
                        // Debounce expensive operations
                        updateMetadataDebounced(content);
                        
                        // If search is active, reapply search after short delay
                        if (isSearchActive && searchTerm) {
                          setTimeout(() => {
                            handleSearch();
                          }, 300);
                        }
                      }}
                      style={{ 
                        height: '500px', 
                        maxHeight: 'none',
                        overflow: 'auto'
                      }}
                      spellCheck={false}
                    />
                    {/* WideArea Mode toggle button */}
                    <Button
                      variant="outline"
                      size="icon"
                      className="absolute bottom-3 right-3 bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 h-8 w-8 rounded-full shadow-md hover:shadow-lg border border-gray-200 dark:border-gray-700"
                      onClick={toggleWideAreaMode}
                      title="Toggle WideArea Mode"
                    >
                      <Maximize2 className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Preview Creative Card */}
            <Card className="w-1/2 shadow-md hover:shadow-lg transition-shadow relative z-10 overflow-hidden">
              <CardHeader className="py-1.5 px-3 flex flex-row items-center justify-between relative z-20 bg-card sticky top-0 shadow-sm border-b border-border">
                <div className="flex items-center gap-1.5 z-10">
                  <CardTitle className="text-base font-medium drop-shadow-sm">Preview</CardTitle>
                  <div 
                    className="relative inline-block group cursor-help"
                    title="Preview not working in dark mode"
                  >
                    <Info className="h-3 w-3 text-gray-400" />
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 px-3 py-1.5 text-xs text-white bg-gray-800 rounded-md whitespace-nowrap pointer-events-none z-30">
                      Preview doesn't work properly in dark mode
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2 border-4 border-transparent border-t-gray-800"></div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1.5 z-10">
                  <div className="flex items-center gap-0.5 z-20">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-4 w-4 relative z-20"
                      onClick={() => setPreviewScale(Math.max(0.25, previewScale - 0.25))}
                      disabled={previewScale <= 0.25}
                      title="Zoom out"
                    >
                      <span className="text-[10px]">-</span>
                    </Button>
                    <span className="text-[10px] text-muted-foreground w-8 text-center">
                      {Math.round(previewScale * 100)}%
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-4 w-4 relative z-20"
                      onClick={() => setPreviewScale(Math.min(2, previewScale + 0.25))}
                      disabled={previewScale >= 2}
                      title="Zoom in"
                    >
                      <span className="text-[10px]">+</span>
                    </Button>
                  </div>
                  <div className="h-5 border-l border-border mx-1"></div>
                  <div className="flex items-center gap-0.5">
                    <Button
                      variant={deviceView === 'pc' ? "secondary" : "ghost"}
                      size="icon"
                      className="h-5 w-5 relative z-20"
                      onClick={() => toggleDeviceView('pc')}
                      title="Desktop view"
                    >
                      <Monitor className="h-3 w-3" />
                    </Button>
                    <Button
                      variant={deviceView === 'tablet' ? "secondary" : "ghost"}
                      size="icon"
                      className="h-5 w-5 relative z-20"
                      onClick={() => toggleDeviceView('tablet')}
                      title="Tablet view"
                    >
                      <Tablet className="h-3 w-3" />
                    </Button>
                    <Button
                      variant={deviceView === 'phone' ? "secondary" : "ghost"}
                      size="icon"
                      className="h-5 w-5 relative z-20"
                      onClick={() => toggleDeviceView('phone')}
                      title="Mobile view"
                    >
                      <Smartphone className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="h-5 border-l border-border mx-1"></div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleGenerateLinks}
                    className="h-7 w-7 hover:bg-blue-50 dark:hover:bg-blue-900/20 relative z-20"
                    title="Tracking Links"
                  >
                    <LinkIcon className="h-4 w-4 text-blue-500" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleRefreshPreview}
                    className="h-7 w-7 hover:bg-green-50 dark:hover:bg-green-900/20 relative z-20"
                    title="Refresh preview"
                    disabled={isPreviewLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${isPreviewLoading ? 'animate-spin text-primary' : 'text-green-500'}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={openPreviewInNewTab}
                    className="h-7 w-7 hover:bg-blue-50 dark:hover:bg-blue-900/20 relative z-20"
                    title="Open in new tab"
                    disabled={!previewContent}
                  >
                    <ExternalLink className="h-4 w-4 text-blue-500" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={togglePreviewWideAreaMode}
                    className="h-7 w-7 hover:bg-gray-50 dark:hover:bg-gray-900/20 relative z-20"
                    title="Toggle Full-Size Preview"
                  >
                    <Maximize2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <div className="relative z-30">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs py-0 px-3 border-teal-200 dark:border-teal-800 bg-white dark:bg-gray-950 text-teal-700 dark:text-teal-400 hover:bg-teal-50 dark:hover:bg-teal-950/30 hover:text-teal-800 dark:hover:text-teal-300 font-normal"
                      onClick={() => {
                        // Open a modal or dialog to select creative
                        console.log('Creative selection would open here');
                        
                        // For demonstration, cycle through available creatives
                        const currentIndex = creatives.findIndex(c => c.id === activeCreativeId);
                        const nextIndex = (currentIndex + 1) % creatives.length;
                        setActiveCreativeId(creatives[nextIndex].id);
                      }}
                    >
                      {activeCreative ? activeCreative.name : "Select creative"}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <div className="min-h-[500px] max-h-[500px] border rounded-md bg-white overflow-auto no-scrollbar"
                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                  {previewContent ? (
                    <div className="relative flex justify-center">
                      <iframe
                        title="HTML Preview"
                        srcDoc={previewContent}
                        className={`border-0 ${deviceView !== 'pc' ? 'mx-auto' : 'w-full'}`}
                        sandbox="allow-same-origin"
                        loading="lazy"
                        style={{ 
                          transform: `scale(${previewScale})`, 
                          transformOrigin: 'top center',
                          width: deviceView === 'pc' ? `${100 / previewScale}%` : 
                                 deviceView === 'tablet' ? '768px' : '375px',
                          height: `${500 / previewScale}px`,
                          boxShadow: deviceView !== 'pc' ? '0 0 10px rgba(0, 0, 0, 0.1)' : 'none',
                          borderRadius: deviceView !== 'pc' ? '8px' : '0'
                        }}
                      />
                    </div>
                  ) : (
                    <div className="text-center text-muted-foreground h-[500px] flex items-center justify-center">
                      Preview will display here
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
      
      {/* Links generation modal */}
      <Dialog open={isLinksModalOpen} onOpenChange={setIsLinksModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Tracking Links</DialogTitle>
            <DialogDescription>
              Extracted links from your HTML content. Assign tags to links and keep image links for tracking.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col gap-6 overflow-auto py-4 flex-grow">
            <div className="flex gap-4 flex-col h-full">
              {/* Domain Card */}
              <Card className="shadow-sm">
                <CardHeader className="py-3 px-4 flex flex-row items-center justify-between">
                  <CardTitle className="text-base">Domain</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="flex space-x-6">
                    <div className="flex items-center space-x-2">
                      <RadioGroup value={selectedDomain} onValueChange={setSelectedDomain} className="flex gap-6">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="body_domain" id="body_domain" />
                          <Label htmlFor="body_domain" className="cursor-pointer">Use [Body_domain]</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="domain" id="domain" />
                          <Label htmlFor="domain" className="cursor-pointer">Use [Domain]</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="placeholder" id="placeholder" />
                          <Label htmlFor="placeholder" className="cursor-pointer">Use [Placeholder]</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Links Card */}
              <Card className="shadow-sm flex-grow">
                <CardHeader className="py-3 px-4 flex flex-row items-center justify-between">
                  <CardTitle className="text-base">Links Table</CardTitle>
                  <div className="text-sm text-muted-foreground">
                    {extractedLinks.filter(link => link.type === 'link').length} links, {extractedLinks.filter(link => link.type === 'image').length} images
                  </div>
                </CardHeader>
                <CardContent className="p-4 overflow-auto" style={{ maxHeight: '50vh' }}>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">#</TableHead>
                        <TableHead className="min-w-[300px]">Link</TableHead>
                        <TableHead className="w-[100px]">Type</TableHead>
                        <TableHead className="w-[150px]">Replace By (TAG)</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {extractedLinks.map((link) => (
                        <TableRow key={link.id}>
                          <TableCell>{link.id}</TableCell>
                          <TableCell className="font-mono text-xs break-all">
                            <a 
                              href={link.url} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-bold hover:underline"
                              onClick={(e) => {
                                // Prevent the click from closing the modal
                                e.stopPropagation();
                              }}
                            >
                              {link.url}
                            </a>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                link.type === 'link' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 
                                                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              }`}>
                                {link.type}
                              </span>
                              <button
                                className="ml-1 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                                title="Jump to this link in HTML editor"
                                onClick={() => jumpToLink(link.url)}
                              >
                                <ArrowUpRight className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                              </button>
                            </div>
                          </TableCell>
                          <TableCell>
                            {link.type === 'link' ? (
                              <Select 
                                value={link.tag} 
                                onValueChange={(value) => updateLinkTag(link.id, value)}
                              >
                                <SelectTrigger className="w-full h-8 text-xs">
                                  <SelectValue placeholder="Select tag" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Click">Click</SelectItem>
                                  <SelectItem value="Open">Open</SelectItem>
                                  <SelectItem value="Unsub">Unsub</SelectItem>
                                  <SelectItem value="Optout">Optout</SelectItem>
                                </SelectContent>
                              </Select>
                            ) : (
                              <span className="text-muted-foreground text-sm">N/A</span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                      {extractedLinks.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                            No links found in the HTML content.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
              
              {/* Statistics Card */}
              <Card className="shadow-sm">
                <CardHeader className="py-3 px-4">
                  <CardTitle className="text-base">Link Statistics</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium mb-2">Link Types</h3>
                      <ul className="space-y-1">
                        <li className="text-sm flex justify-between">
                          <span>Regular links:</span>
                          <span className="font-medium">{extractedLinks.filter(link => link.type === 'link').length}</span>
                        </li>
                        <li className="text-sm flex justify-between">
                          <span>Image links:</span>
                          <span className="font-medium">{extractedLinks.filter(link => link.type === 'image').length}</span>
                        </li>
                        <li className="text-sm flex justify-between border-t pt-1 mt-1">
                          <span>Total:</span>
                          <span className="font-medium">{extractedLinks.length}</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium mb-2">Tag Distribution</h3>
                      <ul className="space-y-1">
                        <li className="text-sm flex justify-between">
                          <span>Click:</span>
                          <span className="font-medium">{extractedLinks.filter(link => link.tag === 'Click').length}</span>
                        </li>
                        <li className="text-sm flex justify-between">
                          <span>Open:</span>
                          <span className="font-medium">{extractedLinks.filter(link => link.tag === 'Open').length}</span>
                        </li>
                        <li className="text-sm flex justify-between">
                          <span>Unsub:</span>
                          <span className="font-medium">{extractedLinks.filter(link => link.tag === 'Unsub').length}</span>
                        </li>
                        <li className="text-sm flex justify-between">
                          <span>Optout:</span>
                          <span className="font-medium">{extractedLinks.filter(link => link.tag === 'Optout').length}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
          
          <DialogFooter className="flex justify-between w-full">
            <Button 
              variant="outline" 
              onClick={() => setIsLinksModalOpen(false)}
            >
              Close
            </Button>
            <div className="flex gap-2">
              <Button 
                onClick={copyLinksToClipboard}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Generate Links
              </Button>
              <Button 
                onClick={() => {
                  // This will be implemented later
                  alert("Replace Links functionality will be implemented soon");
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Replace Links
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Tracking Links modal */}
      <Dialog open={isTrackingLinksModalOpen} onOpenChange={setIsTrackingLinksModalOpen}>
        <DialogContent className="max-w-5xl">
          <DialogHeader>
            <DialogTitle>Tracking Links</DialogTitle>
          </DialogHeader>
          <div className="mt-4 grid gap-6">
            {/* Domain Selection Card */}
            <Card>
              <CardHeader>
                <CardTitle>Domain</CardTitle>
                <CardDescription>
                  Select which domain to use for your tracking links
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={selectedDomain}
                  onValueChange={setSelectedDomain}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="body_domain" id="body_domain" />
                    <Label htmlFor="body_domain">Use [Body_domain]</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="domain" id="domain" />
                    <Label htmlFor="domain">Use [Domain]</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="placeholder" id="placeholder" />
                    <Label htmlFor="placeholder">Use [Placeholder]</Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>
            
            {/* Links Table Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div>
                  <CardTitle>Links</CardTitle>
                  <CardDescription>
                    All tracking links in the template.
                  </CardDescription>
                </div>
                <Button variant="outline" onClick={addTrackingLink}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Link
                </Button>
              </CardHeader>
              <CardContent>
                {/* Table content would go here */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>URL</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {/* Sample row */}
                    <TableRow>
                      <TableCell>https://example.com</TableCell>
                      <TableCell>External</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">Edit</Button>
                        <Button variant="ghost" size="sm">Delete</Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* WideArea Mode dialog */}
      <Dialog open={isWideAreaMode} onOpenChange={setIsWideAreaMode} modal={true}>
        <DialogContent 
          className="max-w-[90vw] max-h-[90vh] w-[90vw] h-[90vh] p-0 overflow-hidden flex flex-col [&>button]:hidden"
        >
          <DialogHeader className="flex flex-row items-center justify-between p-3 border-b">
            <DialogTitle className="text-lg font-medium">HTML Editor - WideArea Mode</DialogTitle>
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                title="Close WideArea Mode"
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogClose>
          </DialogHeader>
          
          <div className="flex-grow overflow-hidden p-4">
            <div className="flex rounded-md border border-input overflow-hidden h-full">
              <div 
                id="widearea-line-numbers" 
                className="bg-muted/50 text-muted-foreground font-mono text-xs p-1 text-right min-w-[2.5rem] overflow-hidden"
              >
                {renderLineNumbers()}
              </div>
              <Textarea 
                className="w-full h-full font-mono text-sm rounded-none border-none resize-none focus-visible:ring-0 focus-visible:ring-offset-0 leading-[1.6rem] tracking-wide selection:bg-blue-200 dark:selection:bg-blue-800/50 selection:shadow-md"
                value={activeCreative.content}
                onChange={(e) => {
                  // Direct state update for content with minimal overhead
                  const content = e.target.value;
                  updateCreativeContent(content);
                  
                  // Debounce expensive operations
                  updateMetadataDebounced(content);
                  
                  // If search is active, reapply search after short delay
                  if (isSearchActive && searchTerm) {
                    setTimeout(() => {
                      handleSearch();
                    }, 300);
                  }
                }}
                spellCheck={false}
                style={{ maxHeight: "none", overflow: "auto" }}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Preview WideArea Mode dialog */}
      <Dialog open={isPreviewWideAreaMode} onOpenChange={setIsPreviewWideAreaMode} modal={true}>
        <DialogContent 
          className="max-w-[80vw] max-h-[98vh] w-[80vw] h-[98vh] p-0 overflow-hidden flex flex-col [&>button]:hidden"
        >
          <DialogHeader className="flex flex-row items-center justify-between p-3 border-b">
            <DialogTitle className="text-lg font-medium">Preview - WideArea Mode</DialogTitle>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-6 w-6 relative z-20"
                  onClick={() => setPreviewScale(Math.max(0.25, previewScale - 0.25))}
                  disabled={previewScale <= 0.25}
                  title="Zoom out"
                >
                  <span className="text-[10px]">-</span>
                </Button>
                <span className="text-xs text-muted-foreground w-12 text-center">
                  {Math.round(previewScale * 100)}%
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-6 w-6 relative z-20"
                  onClick={() => setPreviewScale(Math.min(2, previewScale + 0.25))}
                  disabled={previewScale >= 2}
                  title="Zoom in"
                >
                  <span className="text-[10px]">+</span>
                </Button>
              </div>
              <div className="h-6 border-l border-border mx-1"></div>
              <div className="flex items-center gap-1">
                <Button
                  variant={deviceView === 'pc' ? "secondary" : "ghost"}
                  size="icon"
                  className="h-6 w-6 relative z-20"
                  onClick={() => toggleDeviceView('pc')}
                  title="Desktop view"
                >
                  <Monitor className="h-3.5 w-3.5" />
                </Button>
                <Button
                  variant={deviceView === 'tablet' ? "secondary" : "ghost"}
                  size="icon"
                  className="h-6 w-6 relative z-20"
                  onClick={() => toggleDeviceView('tablet')}
                  title="Tablet view"
                >
                  <Tablet className="h-3.5 w-3.5" />
                </Button>
                <Button
                  variant={deviceView === 'phone' ? "secondary" : "ghost"}
                  size="icon"
                  className="h-6 w-6 relative z-20"
                  onClick={() => toggleDeviceView('phone')}
                  title="Mobile view"
                >
                  <Smartphone className="h-3.5 w-3.5" />
                </Button>
              </div>
              <div className="h-6 border-l border-border mx-1"></div>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={handleRefreshPreview}
                className="h-7 w-7 hover:bg-green-50 dark:hover:bg-green-900/20 relative z-20"
                title="Refresh preview"
                disabled={isPreviewLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isPreviewLoading ? 'animate-spin text-primary' : 'text-green-500'}`} />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={openPreviewInNewTab}
                className="h-7 w-7 hover:bg-blue-50 dark:hover:bg-blue-900/20 relative z-20"
                title="Open in new tab"
                disabled={!previewContent}
              >
                <ExternalLink className="h-4 w-4 text-blue-500" />
              </Button>
              <DialogClose asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  title="Close WideArea Mode"
                >
                  <X className="h-4 w-4" />
                </Button>
              </DialogClose>
            </div>
          </DialogHeader>
          
          <div className="flex-grow overflow-hidden p-4">
            <div className="h-full border rounded-md bg-white overflow-auto no-scrollbar"
              style={{ 
                scrollbarWidth: 'none', 
                msOverflowStyle: 'none',
                position: 'relative',
                maxHeight: 'none',
                overflow: 'auto'
              }}>
              {previewContent ? (
                <div className="relative h-full flex justify-center">
                  <iframe
                    title="HTML Preview"
                    srcDoc={previewContent}
                    className={`border-0 ${deviceView !== 'pc' ? 'mx-auto' : 'w-full'}`}
                    sandbox="allow-same-origin"
                    loading="lazy"
                    style={{ 
                      transform: `scale(${previewScale})`,
                      transformOrigin: 'top center',
                      width: deviceView === 'pc' ? `${100 / previewScale}%` : 
                             deviceView === 'tablet' ? '768px' : '375px',
                      height: `${100 / previewScale}%`,
                      boxShadow: deviceView !== 'pc' ? '0 0 10px rgba(0, 0, 0, 0.1)' : 'none',
                      borderRadius: deviceView !== 'pc' ? '8px' : '0'
                    }}
                  />
                </div>
              ) : (
                <div className="text-center text-muted-foreground h-full flex items-center justify-center">
                  Preview will display here
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Data Management Card */}
      <Card className="w-full shadow-md hover:shadow-lg transition-shadow duration-200 mt-6">
        <CardHeader 
          className="flex flex-row items-center justify-between py-3 pl-4 relative"
        >
          <div className="flex items-center gap-2 flex-grow cursor-pointer"
            onClick={() => {/* Toggle function can be added here */}}
          >
            <CardTitle>Data Management</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {/* Start and Finish inputs */}
            <div className="flex items-center gap-2 mr-2">
              <Label htmlFor="start-input" className="text-sm whitespace-nowrap">Start:</Label>
              <Input 
                id="start-input" 
                type="number" 
                className="w-24 h-8"
                min="0"
                defaultValue="0"
              />
            </div>
            <div className="flex items-center gap-2 mr-2">
              <Label htmlFor="finish-input" className="text-sm whitespace-nowrap">Finish:</Label>
              <Input 
                id="finish-input" 
                type="number" 
                className="w-24 h-8"
                min="0"
                value={finishValue}
                onChange={(e) => setFinishValue(Number(e.target.value))}
              />
            </div>
            
            {/* Quick Select dropdown */}
            <div className="flex items-center gap-2 mr-2">
              <SearchableSelect 
                id="select-type"
                value={selectedTypeValues || []}
                onValueChange={(value) => {
                  // Convert to array if not already
                  const typeValues = Array.isArray(value) ? [...value] : [value];
                  
                  // If empty array is passed, it means all types were deselected
                  if (typeValues.length === 0) {
                    setSelectedTypeValues(null);
                    setSelectedCells([]);
                    return;
                  }
                  
                  // Define all available type values
                  const allTypeOptions = ['fresh', 'clean', 'openers', 'clickers', 'unsub', 'lead', 'otherGeos'];
                  
                  // Check if "all" was just added or removed
                  const hadAll = selectedTypeValues?.includes('all') || false;
                  const hasAll = typeValues.includes('all');
                  
                  // Handle special case for 'all' option
                  if (!hadAll && hasAll) {
                    // "all" was just selected - add all other options
                    typeValues.length = 0; // Clear the array
                    typeValues.push('all', ...allTypeOptions);
                  } else if (hadAll && !hasAll) {
                    // "all" was just deselected - should deselect all other options as well
                    typeValues.length = 0; // Clear the array - deselect everything
                  } else if (hadAll && hasAll) {
                    // "all" is still selected
                    
                    // Check if any option was deselected
                    const previousOptions = selectedTypeValues?.filter(v => v !== 'all') || [];
                    const currentOptions = typeValues.filter(v => v !== 'all');
                    
                    if (previousOptions.length > currentOptions.length) {
                      // An option was deselected while "all" was selected
                      // Remove "all" from selection
                      const allIndex = typeValues.indexOf('all');
                      if (allIndex !== -1) {
                        typeValues.splice(allIndex, 1);
                      }
                    } else {
                      // Make sure all options are selected if "all" is selected
                      allTypeOptions.forEach(option => {
                        if (!typeValues.includes(option)) {
                          typeValues.push(option);
                        }
                      });
                    }
                  }
                  
                  // Update selected type values state
                  setSelectedTypeValues(typeValues.length > 0 ? typeValues : null);
                  
                  // Always clear previous selections
                  setSelectedCells([]);
                  
                  // Skip if no selection (but we've already cleared the cells)
                  if (typeValues.length === 0) return;
                  
                  // Get filtered data
                  const filteredData = getFilteredData();
                  
                  // Select all cells of the chosen types
                  const newSelectedCells = filteredData.flatMap((item: DataItem) => {
                    // We don't need special "all" case handling here since all individual types are already in typeValues
                    return typeValues
                      .filter(value => ['fresh', 'clean', 'openers', 'clickers', 'unsub', 'lead', 'otherGeos'].includes(value))
                      .map(field => ({
                        itemId: item.id,
                        field: field as keyof DataItem,
                        value: item[field as keyof DataItem]
                      }));
                  });
                  
                  setSelectedCells(newSelectedCells);
                }}
                placeholder="Select Types..."
                triggerClassName="w-[180px] h-8"
                searchPlaceholder="Search types..."
                isMulti={true}
                hideSelectedInTrigger={false}
                items={[
                  { 
                    value: "all", 
                    label: (
                      <div className="flex items-center gap-2">
                        <LayoutGrid className="h-4 w-4" />
                        <span>ALL TYPES</span>
                      </div>
                    )
                  },
                  { 
                    value: "fresh", 
                    label: (
                      <div className="flex items-center gap-2">
                        <Snowflake className="h-4 w-4 text-blue-500" />
                        <span>FRESH</span>
                      </div>
                    )
                  },
                  { 
                    value: "clean", 
                    label: (
                      <div className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500" />
                        <span>CLEAN</span>
                      </div>
                    )
                  },
                  { 
                    value: "openers", 
                    label: (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-purple-500" />
                        <span>OPENERS</span>
                      </div>
                    )
                  },
                  { 
                    value: "clickers", 
                    label: (
                      <div className="flex items-center gap-2">
                        <MousePointer className="h-4 w-4 text-orange-500" />
                        <span>CLICKERS</span>
                      </div>
                    )
                  },
                  { 
                    value: "unsub", 
                    label: (
                      <div className="flex items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-500" />
                        <span>UNSUB</span>
                      </div>
                    )
                  },
                  { 
                    value: "lead", 
                    label: (
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span>LEAD</span>
                      </div>
                    )
                  },
                  { 
                    value: "otherGeos", 
                    label: (
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-teal-500" />
                        <span>OTHER GEOS</span>
                      </div>
                    )
                  }
                ]}
              />
            </div>
            
            {/* Filter Toggle Button */}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={toggleFiltersVisibility}
              className="h-8 text-xs gap-1 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700"
            >
              {filtersVisible ? (
                <>
                  <X className="h-3.5 w-3.5" />
                  Hide Filters
                </>
              ) : (
                <>
                  <Search className="h-3.5 w-3.5" />
                  Show Filters
                </>
              )}
            </Button>
            
            <Button 
              variant="default" 
              size="sm" 
              className="bg-purple-600 hover:bg-purple-700 text-white"
              onClick={() => setIsCleanDataModalOpen(true)}
            >
              CLEAN DATA
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 border-blue-200 dark:border-blue-800 bg-white dark:bg-gray-950 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950/30"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        {/* Clean Data Modal */}
        <Dialog open={isCleanDataModalOpen} onOpenChange={setIsCleanDataModalOpen}>
          <DialogContent className="sm:max-w-xl max-h-[90vh] overflow-auto">
            <DialogHeader>
              <DialogTitle className="text-lg font-semibold flex items-center gap-2">
                <Database className="h-5 w-5 text-purple-500" />
                Data Management - Clean Data
              </DialogTitle>
              <DialogDescription>
                Clean specific data types from selected cells
              </DialogDescription>
            </DialogHeader>
            
            {/* Show loading overlay for the entire modal when either loading state is active */}
            {(isSuppressionFileLoading || isCleanDataLoading) && (
              <div className="absolute inset-0 bg-white/80 dark:bg-gray-950/80 flex flex-col items-center justify-center z-50 rounded-lg backdrop-blur-sm">
                <div className="flex flex-col items-center gap-3">
                  <Loader2 className={`h-8 w-8 animate-spin ${isSuppressionFileLoading ? 'text-green-600' : 'text-purple-600'}`} />
                  <p className={`font-medium text-lg ${isSuppressionFileLoading ? 'text-green-600' : 'text-purple-600'}`}>
                    {isSuppressionFileLoading ? 'Generating suppression file...' : 'Cleaning selected data...'}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">This may take a moment</p>
                </div>
              </div>
            )}
            
            {/* Show download link when suppression file is ready */}
            {suppressionFileReady && !isSuppressionFileLoading && !isCleanDataLoading && (
              <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <p className="text-green-700 dark:text-green-400 font-medium">Suppression file is ready</p>
                  </div>
                  <a 
                    href="#" 
                    onClick={(e) => {
                      e.preventDefault();
                      // In a real app, this would be a link to the actual file
                      alert('Downloading test-file.zip...');
                    }}
                    className="flex items-center gap-1.5 px-3 py-1.5 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/30 text-green-700 dark:text-green-400 rounded border border-green-200 dark:border-green-700 text-sm transition-colors"
                  >
                    <span>test-file.zip</span>
                    <Download className="h-4 w-4" />
                  </a>
                </div>
              </div>
            )}
            
            <div className="space-y-4 py-2">
              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">Selected Sponsors:</h3>
                <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md text-sm max-h-[100px] overflow-y-auto">
                  {/* This would display the actual selected sponsors */}
                  <div className="flex items-center gap-2 p-1">
                    <Server className="h-4 w-4 text-blue-500" />
                    <span>Sponsor 1</span>
                  </div>
                  <div className="flex items-center gap-2 p-1">
                    <Server className="h-4 w-4 text-blue-500" />
                    <span>Sponsor 2</span>
                  </div>
                </div>
              </div>
              
              <div className="mb-4">
                <h3 className="text-sm font-medium mb-2">Selected Offers:</h3>
                <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-md text-sm max-h-[100px] overflow-y-auto">
                  {/* This would display the actual selected offers */}
                  <div className="flex items-center gap-2 p-1">
                    <ArrowUpRight className="h-4 w-4 text-emerald-500" />
                    <span>Offer 1</span>
                  </div>
                  <div className="flex items-center gap-2 p-1">
                    <ArrowUpRight className="h-4 w-4 text-emerald-500" />
                    <span>Offer 2</span>
                  </div>
                </div>
              </div>
              
              {/* Removed "Selected Data Types" section */}
            </div>
            
            <DialogFooter className="flex justify-between items-center">
              <Button 
                variant="outline" 
                onClick={() => setIsCleanDataModalOpen(false)}
                disabled={isSuppressionFileLoading || isCleanDataLoading}
              >
                Cancel
              </Button>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  className="border-green-500 text-green-500 hover:bg-green-50 hover:text-green-600"
                  onClick={() => {
                    // Just for test
                    setSuppressionFileReady(false); // Reset the file ready state
                    setIsSuppressionFileLoading(true);
                    // Simulate API call delay
                    setTimeout(() => {
                      setIsSuppressionFileLoading(false);
                      setSuppressionFileReady(true); // Set the file as ready after loading
                    }, 2000);
                  }}
                  disabled={isSuppressionFileLoading || isCleanDataLoading}
                >
                  {isSuppressionFileLoading ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4 mr-1" />
                  )}
                  Get Suppression File
                </Button>
                <Button 
                  variant="default" 
                  className="bg-purple-600 hover:bg-purple-700"
                  onClick={() => {
                    // Just for test
                    setIsCleanDataLoading(true);
                    // Simulate API call delay
                    setTimeout(() => {
                      setIsCleanDataLoading(false);
                    }, 2000);
                  }}
                  disabled={isSuppressionFileLoading || isCleanDataLoading}
                >
                  {isCleanDataLoading && <Loader2 className="h-4 w-4 mr-1 animate-spin" />}
                  Clean Selected Data
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Filters Section - Conditionally Rendered */}
        {filtersVisible && (
          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
            <div className="flex flex-col sm:flex-row items-center gap-3">
              <div className="flex items-center gap-2">
                <Label htmlFor="geo-filter" className="font-medium text-sm whitespace-nowrap">GEO:</Label>
                <SearchableSelect 
                  id="geo-filter"
                  value={geoFilter || []}
                  onValueChange={handleMultiSelectChange(setGeoFilter)}
                  placeholder="All GEOs"
                  triggerClassName="w-[160px] h-8 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700"
                  searchPlaceholder="Search GEOs..."
                  isMulti={true}
                  hideSelectedInTrigger={false}
                  items={[
                    // Display existing GEOs from data first
                    ...existingGeos,
                    // Then add all other countries
                    ...otherCountries
                  ]}
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Label htmlFor="esp-filter" className="font-medium text-sm whitespace-nowrap">ESP:</Label>
                <SearchableSelect 
                  id="esp-filter"
                  value={espFilter || []}
                  onValueChange={handleMultiSelectChange(setEspFilter)}
                  placeholder="All ESPs"
                  triggerClassName="w-[160px] h-8 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700"
                  searchPlaceholder="Search ESPs..."
                  isMulti={true}
                  hideSelectedInTrigger={false}
                  items={[
                    ...getUniqueEsps().map(esp => ({
                      value: esp,
                      label: (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-purple-500" />
                          <span>{esp}</span>
                        </div>
                      )
                    }))
                  ]}
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Label htmlFor="provider-filter" className="font-medium text-sm whitespace-nowrap">PROVIDER:</Label>
                <SearchableSelect 
                  id="provider-filter"
                  value={providerFilter || []}
                  onValueChange={handleMultiSelectChange(setProviderFilter)}
                  placeholder="All Providers"
                  triggerClassName="w-[160px] h-8 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700"
                  searchPlaceholder="Search providers..."
                  isMulti={true}
                  hideSelectedInTrigger={false}
                  items={[
                    ...getUniqueProviders().map(provider => ({
                      value: provider,
                      label: (
                        <div className="flex items-center gap-2">
                          <Server className="h-4 w-4 text-indigo-500" />
                          <span>{provider}</span>
                        </div>
                      )
                    }))
                  ]}
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Label htmlFor="vertical-filter" className="font-medium text-sm whitespace-nowrap">VERTICAL:</Label>
                <SearchableSelect 
                  id="vertical-filter"
                  value={verticalFilter || []}
                  onValueChange={handleMultiSelectChange(setVerticalFilter)}
                  placeholder="All Verticals"
                  triggerClassName="w-[160px] h-8 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700"
                  searchPlaceholder="Search verticals..."
                  isMulti={true}
                  hideSelectedInTrigger={false}
                  items={[
                    ...getUniqueVerticals().map(vertical => ({
                      value: vertical,
                      label: (
                        <div className="flex items-center gap-2">
                          <ArrowUpRight className="h-4 w-4 text-emerald-500" />
                          <span>{vertical}</span>
                        </div>
                      )
                    }))
                  ]}
                />
              </div>
              
              <Button 
                id="reset-filters-btn"
                variant="outline" 
                size="icon"
                onClick={resetFilters}
                className="h-8 w-8 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-900 transition-all"
                disabled={
                  (!geoFilter || geoFilter.length === 0) && 
                  (!espFilter || espFilter.length === 0) && 
                  (!providerFilter || providerFilter.length === 0) && 
                  (!verticalFilter || verticalFilter.length === 0)
                }
                title={
                  (!geoFilter || geoFilter.length === 0) && 
                  (!espFilter || espFilter.length === 0) && 
                  (!providerFilter || providerFilter.length === 0) && 
                  (!verticalFilter || verticalFilter.length === 0)
                  ? "No active filters" 
                  : "Reset Filters"
                }
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
        
        <CardContent className="p-4">
          <div className="rounded-md">
            
            <div className="overflow-auto border rounded-md">
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="hover:bg-muted/50">
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('name')}>
                      <div className="flex items-center">
                        NAME
                        {getSortIcon('name')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('geo')}>
                      <div className="flex items-center">
                        GEO
                        {getSortIcon('geo')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('esp')}>
                      <div className="flex items-center">
                        ESP
                        {getSortIcon('esp')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('provider')}>
                      <div className="flex items-center">
                        PROVIDER
                        {getSortIcon('provider')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('vertical')}>
                      <div className="flex items-center">
                        VERTICAL
                        {getSortIcon('vertical')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('fresh')}>
                      <div className="flex items-center">
                        FRESH
                        {getSortIcon('fresh')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('clean')}>
                      <div className="flex items-center">
                        CLEAN
                        {getSortIcon('clean')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('openers')}>
                      <div className="flex items-center">
                        OPENERS
                        {getSortIcon('openers')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('clickers')}>
                      <div className="flex items-center">
                        CLICKERS
                        {getSortIcon('clickers')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('unsub')}>
                      <div className="flex items-center">
                        UNSUB
                        {getSortIcon('unsub')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('lead')}>
                      <div className="flex items-center">
                        LEAD
                        {getSortIcon('lead')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('otherGeos')}>
                      <div className="flex items-center">
                        OTHER GEOS
                        {getSortIcon('otherGeos')}
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getSortedData().map((item) => (
                    <TableRow key={item.id} className="border-b transition-colors hover:bg-muted/50">
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <CountryFlag country={item.geo} />
                          <span className="uppercase">{item.geo}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.esp}</TableCell>
                      <TableCell>{item.provider}</TableCell>
                      <TableCell>{item.vertical}</TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'fresh', item.fresh)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'fresh') ? 'bg-teal-100 dark:bg-teal-900/30' : ''} hover:bg-teal-50 dark:hover:bg-teal-900/20`}
                      >
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-teal-500" />
                          {item.fresh}
                        </div>
                      </TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'clean', item.clean)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'clean') ? 'bg-green-100 dark:bg-green-900/30' : ''} hover:bg-green-50 dark:hover:bg-green-900/20`}
                      >
                        <div className="flex items-center">
                          <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                          {item.clean}
                        </div>
                      </TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'openers', item.openers)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'openers') ? 'bg-blue-100 dark:bg-blue-900/30' : ''} hover:bg-blue-50 dark:hover:bg-blue-900/20`}
                      >
                        <div className="flex items-center">
                          <Mail className="h-3 w-3 mr-1 text-blue-500" />
                          {item.openers}
                        </div>
                      </TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'clickers', item.clickers)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'clickers') ? 'bg-indigo-100 dark:bg-indigo-900/30' : ''} hover:bg-indigo-50 dark:hover:bg-indigo-900/20`}
                      >
                        <div className="flex items-center">
                          <MousePointer className="h-3 w-3 mr-1 text-indigo-500" />
                          {item.clickers}
                        </div>
                      </TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'unsub', item.unsub)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'unsub') ? 'bg-red-100 dark:bg-red-900/30' : ''} hover:bg-red-50 dark:hover:bg-red-900/20`}
                      >
                        <div className="flex items-center">
                          <UserMinus className="h-3 w-3 mr-1 text-red-500" />
                          {item.unsub}
                        </div>
                      </TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'lead', item.lead)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'lead') ? 'bg-purple-100 dark:bg-purple-900/30' : ''} hover:bg-purple-50 dark:hover:bg-purple-900/20`}
                      >
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1 text-purple-500" />
                          {item.lead}
                        </div>
                      </TableCell>
                      <TableCell 
                        onClick={() => toggleCellSelection(item.id, 'otherGeos', item.otherGeos)}
                        className={`cursor-pointer ${isCellSelected(item.id, 'otherGeos') ? 'bg-amber-100 dark:bg-amber-900/30' : ''} ${item.name === 'nl_just1.0.2' && item.otherGeos === 10 ? 'text-blue-500 font-semibold' : ''} hover:bg-amber-50 dark:hover:bg-amber-900/20`}
                      >
                        <div className="flex items-center">
                          <Globe className="h-3 w-3 mr-1 text-amber-500" />
                          {item.otherGeos}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground flex items-center">
                <span>
                  Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, filteredItems.length)} of {filteredItems.length} entries
                </span>
                <div className="flex items-center ml-4">
                  <span className="mr-2 text-xs">Show:</span>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => {
                      setItemsPerPage(Number(value));
                      setCurrentPage(1); // Reset to page 1
                    }}
                  >
                    <SelectTrigger className="h-7 w-16 text-xs border-gray-200">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="15">15</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {selectedCells.length > 0 && (
                  <span className="ml-2">
                    | Selected: {selectedCells.length} cells
                    {selectedCells.length === 1 && ` (Value: ${selectedCells[0].value})`}
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1} 
                  className="h-8 px-3"
                >
                  Previous
                </Button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button 
                    key={page}
                    variant="outline" 
                    size="sm" 
                    onClick={() => goToPage(page)}
                    className={`h-8 w-8 p-0 ${
                      currentPage === page 
                        ? 'bg-purple-100 dark:bg-purple-900 border-purple-200 dark:border-purple-800 text-purple-700 dark:text-purple-300' 
                        : ''
                    }`}
                  >
                    {page}
                  </Button>
                ))}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages} 
                  className="h-8 px-3"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Test Results Card */}
      <Card className="w-full shadow-md hover:shadow-lg transition-shadow duration-200 mt-6">
        <CardHeader 
          className="flex flex-row items-center justify-between py-3 pl-4 relative"
        >
          <div className="flex items-center gap-2 flex-grow">
            <CardTitle>Test Results</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium text-muted-foreground">
              {completedTests} of {totalTests} tests completed
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Progress indicator */}
          <div className="mb-5">
            <div className="w-full bg-secondary rounded-full h-2.5">
              <div 
                className="h-2.5 rounded-full"
                style={{ 
                  width: `${Math.min(100, (completedTests / totalTests) * 100)}%`,
                  backgroundColor: completedTests > totalTests ? 'var(--amber-500, #f59e0b)' : 'var(--blue-600, #2563eb)'
                }}
              ></div>
            </div>
          </div>

          {/* Test results table */}
          <div className="overflow-x-auto border rounded-md">
            <table className="min-w-full divide-y divide-border">
              <thead>
                <tr className="bg-muted/50">
                  <th 
                    scope="col" 
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group"
                    onClick={() => sortTestResults('server')}
                  >
                    <div className="flex items-center">
                      <span>SERVER</span>
                      {getTestSortIcon('server')}
                    </div>
                  </th>
                  <th 
                    scope="col" 
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group"
                    onClick={() => sortTestResults('interface')}
                  >
                    <div className="flex items-center">
                      <span>IP/INTERFACE</span>
                      {getTestSortIcon('interface')}
                    </div>
                  </th>
                  <th 
                    scope="col" 
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group"
                    onClick={() => sortTestResults('returnPath')}
                  >
                    <div className="flex items-center">
                      <span>RETURN PATH</span>
                      {getTestSortIcon('returnPath')}
                    </div>
                  </th>
                  <th 
                    scope="col" 
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group"
                    onClick={() => sortTestResults('receivedEmail')}
                  >
                    <div className="flex items-center">
                      <span>RECEIVED EMAIL</span>
                      {getTestSortIcon('receivedEmail')}
                    </div>
                  </th>
                  <th 
                    scope="col" 
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group"
                    onClick={() => sortTestResults('testStatus')}
                  >
                    <div className="flex items-center">
                      <span>TEST STATUS</span>
                      {getTestSortIcon('testStatus')}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {getSortedTestResults().map((result, index) => (
                  <tr 
                    key={result.id} 
                    className={`${index % 2 === 0 ? 'bg-card' : 'bg-muted/10'} hover:bg-muted/50 transition-colors duration-150`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center">
                        <Server className="h-3.5 w-3.5 mr-2 text-blue-500" />
                      {result.server}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center">
                        <LinkIcon className="h-3.5 w-3.5 mr-2 text-indigo-500" />
                      {result.interface}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground max-w-[200px] truncate group relative">
                      <div className="flex items-center">
                        <ArrowUpRight className="h-3.5 w-3.5 mr-2 text-purple-500 flex-shrink-0" />
                      <span 
                          className="inline-block truncate max-w-[400px]" 
                        title={result.returnPath}
                      >
                        {result.returnPath}
                      </span>
                      </div>
                      <div className="absolute left-0 top-10 hidden group-hover:block bg-popover shadow-md rounded px-2 py-1 text-xs z-10 whitespace-normal max-w-[300px] border border-border">
                        {result.returnPath}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Mail className="h-3.5 w-3.5 mr-2 text-amber-500" />
                      {result.receivedEmail}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={`px-3 py-1 inline-flex items-center gap-1.5 text-xs leading-5 font-semibold rounded-full ${
                          result.testStatus === 'Success' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                            : result.testStatus === 'Failed'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                        }`}>
                          {result.testStatus === 'Success' && (
                            <CheckCircle className="w-3.5 h-3.5" />
                          )}
                          {result.testStatus === 'Failed' && (
                            <XCircle className="w-3.5 h-3.5" />
                          )}
                          {result.testStatus === 'Pending' && (
                            <Clock className="w-3.5 h-3.5" />
                          )}
                          {result.testStatus}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination info */}
          <div className="text-sm text-muted-foreground mt-4">
            Showing 1 to {testResults.length} of {testResults.length} entries
          </div>
        </CardContent>
      </Card>
      
      {/* Header WideArea Mode dialog */}
      <Dialog open={isHeaderWideAreaMode} onOpenChange={setIsHeaderWideAreaMode} modal={true}>
        <DialogContent 
          className="max-w-[90vw] max-h-[90vh] w-[90vw] h-[90vh] p-0 overflow-hidden flex flex-col [&>button]:hidden"
        >
          <DialogHeader className="flex flex-row items-center justify-between p-3 border-b">
            <DialogTitle className="text-lg font-medium">Header Editor - WideArea Mode</DialogTitle>
            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                title="Close WideArea Mode"
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogClose>
          </DialogHeader>
          
          <div className="flex-grow overflow-hidden p-4">
            <div className="flex rounded-md border border-input overflow-hidden h-full">
              <Textarea 
                className="w-full h-full font-mono text-sm rounded-none border-none resize-none focus-visible:ring-0 focus-visible:ring-offset-0 leading-[1.6rem] tracking-wide selection:bg-blue-200 dark:selection:bg-blue-800/50 selection:shadow-md"
                value={activeHeader.content}
                onChange={(e) => updateHeaderContent(e.target.value)}
                spellCheck={false}
                style={{ maxHeight: "none", overflow: "auto" }}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </ContentLayout>
  );
} 
