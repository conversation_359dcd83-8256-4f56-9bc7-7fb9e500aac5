"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { LogOut, User, Settings, ChevronDown, ChevronRight, ArrowUp } from "lucide-react";
import { useState, useRef, useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { useSidebar } from "@/hooks/use-sidebar";
import { useUISettings } from "@/hooks/use-ui-settings";
import { useStore } from "@/hooks/use-store";

interface UserNavProps {
  user?: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
}

export function UserNav({ user = {} }: UserNavProps) {
  const router = useRouter();
  const sidebar = useStore(useSidebar, (x) => x);
  const uiSettings = useStore(useUISettings, (x) => x);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isSubmenuOpen, setIsSubmenuOpen] = useState(false);
  const [isSubmenuHovered, setIsSubmenuHovered] = useState(false);
  const [isUISettingsSubmenuOpen, setIsUISettingsSubmenuOpen] = useState(false);
  const [isUISettingsSubmenuHovered, setIsUISettingsSubmenuHovered] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const submenuRef = useRef<HTMLDivElement>(null);
  const submenuTriggerRef = useRef<HTMLDivElement>(null);
  const uiSettingsSubmenuRef = useRef<HTMLDivElement>(null);
  const uiSettingsSubmenuTriggerRef = useRef<HTMLDivElement>(null);
  
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const uiSettingsHoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  if (!sidebar || !uiSettings) return null;
  const { settings, setSettings } = sidebar;
  const { settings: uiSettingsValues, setSettings: setUISettings, setSidebarSettings } = uiSettings;

  const handleSignOut = async () => {
    // Add your sign out logic here
    // For example:
    // await logout(); // Call your logout API
    router.push('/login');
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setIsSubmenuOpen(false);
        setIsUISettingsSubmenuOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle hover timing for submenu
  useEffect(() => {
    if (isSubmenuHovered) {
      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
        hoverTimeoutRef.current = null;
      }
      
      // Open submenu with slight delay
      hoverTimeoutRef.current = setTimeout(() => {
        setIsSubmenuOpen(true);
      }, 200);
    } else {
      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
        hoverTimeoutRef.current = null;
      }
      
      // Close after delay to allow moving to submenu
      hoverTimeoutRef.current = setTimeout(() => {
        if (!isSubmenuHovered) {
          setIsSubmenuOpen(false);
        }
      }, 300);
    }

    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, [isSubmenuHovered]);

  // Handle hover timing for UI Settings submenu
  useEffect(() => {
    if (isUISettingsSubmenuHovered) {
      // Clear any existing timeout
      if (uiSettingsHoverTimeoutRef.current) {
        clearTimeout(uiSettingsHoverTimeoutRef.current);
        uiSettingsHoverTimeoutRef.current = null;
      }
      
      // Open submenu with slight delay
      uiSettingsHoverTimeoutRef.current = setTimeout(() => {
        setIsUISettingsSubmenuOpen(true);
      }, 200);
    } else {
      // Clear any existing timeout
      if (uiSettingsHoverTimeoutRef.current) {
        clearTimeout(uiSettingsHoverTimeoutRef.current);
        uiSettingsHoverTimeoutRef.current = null;
      }
      
      // Close after delay to allow moving to submenu
      uiSettingsHoverTimeoutRef.current = setTimeout(() => {
        if (!isUISettingsSubmenuHovered) {
          setIsUISettingsSubmenuOpen(false);
        }
      }, 300);
    }

    return () => {
      if (uiSettingsHoverTimeoutRef.current) {
        clearTimeout(uiSettingsHoverTimeoutRef.current);
      }
    };
  }, [isUISettingsSubmenuHovered]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
    if (!isDropdownOpen) {
      setIsSubmenuOpen(false);
      setIsUISettingsSubmenuOpen(false);
    }
  };

  const toggleSubmenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsSubmenuOpen(!isSubmenuOpen);
  };

  const toggleUISettingsSubmenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsUISettingsSubmenuOpen(!isUISettingsSubmenuOpen);
  };

  return (
    <div className="flex items-center relative" ref={dropdownRef}>
      {/* Custom dropdown button */}
      <Button
        variant="ghost"
        className={cn(
          "flex items-center gap-1 px-3 py-1.5 rounded-md cursor-pointer transition-colors duration-150",
          isHovered || isDropdownOpen ? "bg-secondary" : "hover:bg-secondary/50"
        )}
        onClick={toggleDropdown}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Settings className={cn(
          "h-4 w-4 transition-colors duration-150",
          isHovered || isDropdownOpen ? "text-primary" : "text-muted-foreground"
        )} />
        <span className="ml-1 text-sm font-medium">Settings</span>
        <ChevronDown className={cn(
          "h-3.5 w-3.5 ml-0.5 transition-all duration-150",
          isHovered || isDropdownOpen ? "text-primary" : "text-muted-foreground",
          isDropdownOpen && "transform rotate-180"
        )} />
      </Button>

      {/* Custom dropdown menu */}
      {isDropdownOpen && (
        <div className="absolute right-0 top-full mt-2 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md z-50 animate-in fade-in-0 zoom-in-95 slide-in-from-top-2">
          {/* User info */}
          <div className="px-2 py-1.5 text-sm font-semibold">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{user?.name || 'Rahimi Monaim'}</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user?.email || '<EMAIL>'}
              </p>
            </div>
          </div>
          <div className="-mx-1 my-1 h-px bg-muted" />
          
          {/* Navigation links */}
          <div className="px-1">
            <Link 
              href="/account" 
              className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-secondary/50"
              onClick={() => setIsDropdownOpen(false)}
            >
              <User className="w-4 h-4 mr-3 text-muted-foreground" />
              Account
            </Link>
          </div>
          <div className="-mx-1 my-1 h-px bg-muted" />
          
          {/* UI Settings submenu */}
          <div 
            className="relative px-1" 
            ref={uiSettingsSubmenuTriggerRef}
            onMouseEnter={() => setIsUISettingsSubmenuHovered(true)}
            onMouseLeave={() => setIsUISettingsSubmenuHovered(false)}
          >
            <div 
              className="relative flex justify-between cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-secondary/50"
              onClick={toggleUISettingsSubmenu}
            >
              <span className="font-medium">UI Settings</span>
              <ChevronRight className={cn(
                "ml-auto h-4 w-4 transition-transform duration-200",
                isUISettingsSubmenuOpen && "transform rotate-90"
              )} />
            </div>
            
            {/* UI Settings submenu */}
            {isUISettingsSubmenuOpen && (
              <div 
                className="absolute right-full top-0 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md z-50 animate-in fade-in-0 zoom-in-95 slide-in-from-right-1 -mr-1"
                ref={uiSettingsSubmenuRef}
                onMouseEnter={() => setIsUISettingsSubmenuHovered(true)}
                onMouseLeave={() => setIsUISettingsSubmenuHovered(false)}
              >
                <div className="px-2 py-2 space-y-3">
                  {/* Enhanced Sidebar Settings in UI Settings submenu */}
                  <div className="pt-2 border-t border-border">
                    <p className="text-xs font-medium text-muted-foreground mb-2">Sidebar Options</p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="disable-sidebar"
                          checked={uiSettingsValues.sidebar.disabled}
                          onCheckedChange={(checked) => {
                            setSidebarSettings({ disabled: checked });
                            // Keep the old sidebar state in sync during transition
                            setSettings({ disabled: checked });
                          }}
                        />
                        <Label htmlFor="disable-sidebar" className="text-sm cursor-pointer">Disable Sidebar</Label>
                      </div>
                      
                      {!uiSettingsValues.sidebar.disabled && (
                        <>
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="hover-sidebar"
                              checked={uiSettingsValues.sidebar.isHoverOpen}
                              onCheckedChange={(checked) => {
                                setSidebarSettings({ isHoverOpen: checked });
                                // Keep the old sidebar state in sync during transition
                                setSettings({ isHoverOpen: checked });
                              }}
                            />
                            <Label htmlFor="hover-sidebar" className="text-sm cursor-pointer">Hover to Open</Label>
                          </div>
                          
                          {/* Add "Use Navbar" option */}
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="use-navbar"
                              checked={uiSettingsValues.sidebar.useNavbar}
                              onCheckedChange={(checked) => {
                                setSidebarSettings({ useNavbar: checked });
                                // Keep the old sidebar state in sync during transition
                                setSettings({ disabled: checked }); // Disable sidebar when using navbar
                              }}
                            />
                            <Label htmlFor="use-navbar" className="text-sm cursor-pointer">Use Navbar</Label>
                          </div>
                          
                          <div className="flex flex-col space-y-1 mt-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="hover-delay" className="text-xs text-muted-foreground mr-2">
                                Hover Delay:
                              </Label>
                              <span className="text-xs font-medium">{uiSettingsValues.sidebar.submenuHoverDelay}ms</span>
                            </div>
                            <input
                              id="hover-delay"
                              type="range"
                              min="100"
                              max="1000"
                              step="50"
                              value={uiSettingsValues.sidebar.submenuHoverDelay}
                              onChange={(e) => {
                                const value = parseInt(e.target.value);
                                setSidebarSettings({ submenuHoverDelay: value });
                                // Keep the old sidebar state in sync during transition
                                setSettings({ submenuHoverDelay: value });
                              }}
                              className="w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer"
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="-mx-1 my-1 h-px bg-muted" />
          
          {/* Sign out */}
          <button 
            className="relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-secondary/50"
            onClick={() => {
              handleSignOut();
              setIsDropdownOpen(false);
            }}
          >
            <LogOut className="w-4 h-4 mr-3 text-muted-foreground" />
            Sign out
          </button>
        </div>
      )}
    </div>
  );
}

