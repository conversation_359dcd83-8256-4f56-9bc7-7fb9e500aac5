import { ModeToggle } from "@/components/mode-toggle";
import { UserNav } from "@/components/admin-panel/user-nav";
import { SheetMenu } from "@/components/admin-panel/sheet-menu";
import { Mail, Maximize, Minimize } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { Menu } from "@/components/admin-panel/menu";
import { useSidebar, useHydratedSidebar } from "@/hooks/use-sidebar";
import { useUISettings } from "@/hooks/use-ui-settings";
import { useStore } from "@/hooks/use-store";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";

interface NavbarProps {
  title: string;
  actions?: React.ReactNode;
}

// Small logo component for the navbar
const SmallSendFluxLogo = () => (
  <div className="relative mr-2">
    <Mail className="w-4 h-4 text-primary stroke-[2.5px]" />
    <div className="absolute w-1 h-1 rounded-full bg-primary -top-0.5 -right-0.5 animate-pulse" />
  </div>
);

export function Navbar({ title, actions }: NavbarProps) {
  // Get sidebar and UI settings
  const { hydrated: sidebarHydrated } = useHydratedSidebar();
  const sidebar = useStore(useSidebar, (x) => x);
  const uiSettings = useStore(useUISettings, (x) => x);
  
  // State to track full screen mode
  const [isFullScreen, setIsFullScreen] = useState(false);
  
  // Simplified function to toggle full screen mode
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
        .catch(err => console.log("Could not enter fullscreen:", err));
    } else {
      document.exitFullscreen()
        .catch(err => console.log("Could not exit fullscreen:", err));
    }
  };
  
  // Listen for full screen change events
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullScreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullScreenChange);
  }, []);
  
  // Check if we should show the menu in navbar
  const showMenuInNavbar = sidebarHydrated && 
    sidebar && 
    uiSettings?.settings?.sidebar?.useNavbar;
  
  // Update document title when component mounts or title changes
  useEffect(() => {
    document.title = `SendFlux | ${title}`;
  }, [title]);

  return (
    <header className="sticky top-0 z-10 w-full bg-background/95 shadow backdrop-blur supports-[backdrop-filter]:bg-background/60 dark:shadow-secondary">
      <div className="mx-3 sm:mx-6 flex h-12 items-center">
        <div className="flex items-center space-x-3 lg:space-x-0">
          {/* Only show SheetMenu when not using menu in navbar */}
          {!showMenuInNavbar && <SheetMenu />}
          <div className="flex items-center">
            <SmallSendFluxLogo />
            <h1 className="font-bold">{title}</h1>
          </div>
        </div>
        
        {/* Add horizontal menu if useNavbar is enabled */}
        {showMenuInNavbar && (
          <div className="flex-1 px-4 overflow-x-auto hide-scrollbar">
            <div className="flex items-center h-full">
              <Menu isOpen={true} horizontal={true} />
            </div>
          </div>
        )}
        
        <div className={cn(
          "flex items-center justify-end space-x-2",
          showMenuInNavbar ? "ml-2" : "flex-1"
        )}>
          {actions}
          
          {/* Full Screen Toggle Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={toggleFullScreen}
            title={isFullScreen ? "Exit Full Screen" : "Full Screen"}
          >
            {isFullScreen ? (
              <Minimize className="h-4 w-4" />
            ) : (
              <Maximize className="h-4 w-4" />
            )}
            <span className="sr-only">Toggle Full Screen</span>
          </Button>
          
          <ModeToggle />
          <UserNav />
        </div>
      </div>
    </header>
  );
}
