import {
  LayoutGrid,
  LucideIcon,
  Database,
  Gift,
  Send,
  Mail,
  Server,
  Users,
  Package,
  Building,
  List,
  Settings,
  Globe,
  Key,
  Code,
  Workflow,
  MessageSquare,
  FileText,
  Wrench,
  Activity,
  Layers,
  MonitorSmartphone,
  Plus
} from "lucide-react";

type Submenu = {
  href: string;
  label: string;
  active?: boolean;
  submenus?: Submenu[];
  icon?: LucideIcon;
};

type Menu = {
  href: string;
  label: string;
  active?: boolean;
  icon: LucideIcon;
  submenus?: Submenu[];
};

type Group = {
  groupLabel: string;
  menus: Menu[];
};

export function getMenuList(pathname: string): Group[] {
  return [
    {
      groupLabel: "",
      menus: [
        {
          href: "/dashboard",
          label: "Dashboard",
          icon: LayoutGrid
        },
        {
          href: "",
          label: "Production",
          icon: Activity,
          submenus: [
            {
              href: "/production/send-page",
              label: "Send Page",
              icon: Send
            },
            {
              href: "/production/mta-drops-monitor",
              label: "MTA Drops Monitor",
              icon: Activity
            },
            {
              href: "/production/mta-tests-monitor",
              label: "MTA Tests Monitor",
              icon: Activity
            },
            {
              href: "/production/smtp-drops-monitor",
              label: "SMTP Drops Monitor",
              icon: Activity
            },
            {
              href: "/production/smtp-tests-monitor",
              label: "SMTP Tests Monitor",
              icon: Activity
            }
          ]
        },
        {
          href: "",
          label: "Offer Manager",
          icon: Gift,
          submenus: [
            {
              href: "",
              label: "Sponsors",
              icon: Users,
              submenus: [
                {
                  href: "/offer-manager/sponsors",
                  label: "Sponsors List",
                  icon: List
                },
                {
                  href: "/offer-manager/sponsors/add",
                  label: "Add Sponsor",
                  icon: Plus
                }
              ]
            },
            {
              href: "",
              label: "Offers",
              icon: Package,
              submenus: [
                {
                  href: "/offer-manager/offers",
                  label: "Offers List",
                  icon: List
                },
                {
                  href: "/offer-manager/offers/add",
                  label: "Add Offer",
                  icon: Plus
                }
              ]
            }
          ]
        },
        {
          href: "",
          label: "Servers Manager",
          icon: Server,
          submenus: [
            {
              href: "",
              label: "Server Provider",
              icon: Building,
              submenus: [
                {
                  href: "/servers-manager/server-provider/providers",
                  label: "Providers List",
                  icon: List
                },
                {
                  href: "/servers-manager/server-provider/providers/add",
                  label: "Add New Provider",
                  icon: Plus
                }
              ]
            },
            {
              href: "",
              label: "Servers",
              icon: MonitorSmartphone,
              submenus: [
                {
                  href: "/servers-manager/mta-servers",
                  label: "MTA Servers List",
                  icon: List
                },
                {
                  href: "/servers-manager/mta-servers/add",
                  label: "Add MTA Servers",
                  icon: Plus
                }
              ]
            },
            {
              href: "",
              label: "SMTP",
              icon: Mail,
              submenus: [
                {
                  href: "/servers-manager/smtp",
                  label: "SMTP Servers List",
                  icon: List
                },
                {
                  href: "/servers-manager/smtp/add",
                  label: "Add SMTP Server",
                  icon: Plus
                }
              ]
            }
          ]
        },
        {
          href: "",
          label: "PMTA Manager",
          icon: Workflow,
          submenus: [
            {
              href: "/pmta-manager/overview",
              label: "Overview",
              icon: Activity
            },
            {
              href: "/pmta-manager/configuration",
              label: "Configuration",
              icon: Settings
            },
            {
              href: "/pmta-manager/logs",
              label: "Logs",
              icon: FileText
            }
          ]
        },
        {
          href: "",
          label: "DNS Manager",
          icon: Globe,
          submenus: [
            {
              href: "",
              label: "Domains",
              icon: Globe,
              submenus: [
                {
                  href: "/dns-manager/domains",
                  label: "Domains List",
                  icon: List
                },
                {
                  href: "/dns-manager/domains/add",
                  label: "Add Domain",
                  icon: Plus
                },
                {
                  href: "/dns-manager/domains/verify",
                  label: "Verify Domains",
                  icon: Key
                }
              ]
            },
            {
              href: "",
              label: "API",
              icon: Code,
              submenus: [
                {
                  href: "/dns-manager/api/keys",
                  label: "API Keys",
                  icon: Key
                },
                {
                  href: "/dns-manager/api/endpoints",
                  label: "API Endpoints",
                  icon: Code
                },
                {
                  href: "/dns-manager/api/usage",
                  label: "API Usage",
                  icon: Activity
                }
              ]
            },
            {
              href: "",
              label: "Tools",
              icon: Wrench,
              submenus: [
                {
                  href: "/dns-manager/tools/ip-checker",
                  label: "IP Checker",
                  icon: Globe
                },
                {
                  href: "/dns-manager/tools/domain-tools",
                  label: "Domain Tools",
                  icon: Wrench
                },
                {
                  href: "/dns-manager/tools/text-tools",
                  label: "Text Tools",
                  icon: FileText
                }
              ]
            }
          ]
        },
        {
          href: "",
          label: "Data Manager",
          icon: Database,
          submenus: [
            {
              href: "/data-manager/data-lists",
              label: "Data Lists",
              icon: FileText
            },
            {
              href: "/data-manager/isps",
              label: "ISPs List",
              icon: Building
            },
            {
              href: "/data-manager/data-lists/black-lists",
              label: "Black List",
              icon: FileText
            },
            {
              href: "/data-manager/data-lists/hard-bounce",
              label: "Hard Bounce",
              icon: FileText
            }
          ]
        }
      ]
    }
  ];
}
