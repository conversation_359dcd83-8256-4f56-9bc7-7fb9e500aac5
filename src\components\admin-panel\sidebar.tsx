"use client";
import { Menu } from "@/components/admin-panel/menu";
import { SidebarToggle } from "@/components/admin-panel/sidebar-toggle";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useSidebar, useHydratedSidebar } from "@/hooks/use-sidebar";
import { useStore } from "@/hooks/use-store";
import { cn } from "@/lib/utils";
import { Mail, Feather } from "lucide-react";
import Link from "next/link";
import { ClientOnly } from "@/components/client-only";
import { useEffect, useRef, useState, useMemo } from "react";

// Custom logo component
const SendFluxLogo = ({ size = 'default' }: { size?: 'small' | 'default' | 'large' }) => {
  const sizeClasses = {
    small: "w-4 h-4",
    default: "w-5 h-5",
    large: "w-6 h-6"
  };
  
  return (
    <div className="relative">
      <Mail className={`${sizeClasses[size]} text-primary stroke-[2.5px]`} />
      <div className="absolute w-1 h-1 rounded-full bg-primary -top-0.5 -right-0.5 animate-pulse" />
    </div>
  );
};

export function Sidebar() {
  // All hooks must be called at the top level - before any conditional logic
  // This ensures we comply with React's Rules of Hooks (hooks must be called in the same order on every render)
  const { hydrated } = useHydratedSidebar();
  const sidebar = useStore(useSidebar, (x) => x);
  const hoverTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Use this for rendering when hover is enabled
  const [hoveredSidebarContent, setHoveredSidebarContent] = useState<React.ReactNode | null>(null);
  
  // Get the hover mode status from settings
  const isHoverModeEnabled = useMemo(() => {
    if (!sidebar || !hydrated) return false;
    return sidebar.settings.isHoverOpen;
  }, [sidebar, hydrated]);
  
  // Only run the hover effect if hover mode is enabled
  useEffect(() => {
    if (!sidebar || !hydrated || !isHoverModeEnabled) return;
    
    const { isOpen, setIsOpen, settings } = sidebar;
    const { submenuHoverDelay } = settings;
    const isHover = sidebar.isHover;
    
    // Clear any existing timer
    if (hoverTimerRef.current) {
      clearTimeout(hoverTimerRef.current);
      hoverTimerRef.current = null;
    }
    
    // Set a new timer with the configured delay
    hoverTimerRef.current = setTimeout(() => {
      if (isHover && !isOpen) {
        setIsOpen(true);
      } else if (!isHover && isOpen) {
        setIsOpen(false);
      }
    }, submenuHoverDelay || 300);
    
    // Cleanup function
    return () => {
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
        hoverTimerRef.current = null;
      }
    };
  }, [sidebar, hydrated, isHoverModeEnabled]);
  
  // Render sidebar content for hover-enabled mode
  useEffect(() => {
    if (!sidebar || !hydrated || !isHoverModeEnabled) {
      setHoveredSidebarContent(null);
      return;
    }
    
    const { isOpen, toggleOpen, getOpenState, setIsHover, settings } = sidebar;
    
    const content = (
      <aside
        className={cn(
          "fixed top-0 left-0 z-20 h-screen -translate-x-full lg:translate-x-0 transition-[width] ease-in-out duration-300",
          !getOpenState() ? "w-[60px] hover:shadow-lg" : "w-56",
          settings.disabled && "hidden"
        )}
      >
        <div
          onMouseEnter={() => setIsHover(true)}
          onMouseLeave={() => setIsHover(false)}
          className="relative h-full flex flex-col px-2 py-4 pb-10 overflow-hidden bg-background border-r transition-all duration-300 ease-in-out"
        >
          <Button
            className={cn(
              "mb-1",
              !getOpenState() ? "justify-center px-2" : "justify-center w-full"
            )}
            variant="link"
            asChild
          >
            <Link href="/dashboard" className="flex items-center">
              <span className={cn(
                "flex items-center min-w-[24px]",
                getOpenState() ? "hidden" : ""
              )}>
                <SendFluxLogo />
              </span>
              <div
                className={cn(
                  "whitespace-nowrap",
                  !getOpenState()
                    ? "opacity-0 w-0 ml-0"
                    : "opacity-100 w-auto flex items-center"
                )}
              >
                <SendFluxLogo size="small" />
                <h1 className="font-bold text-lg text-primary ml-2">SendFlux</h1>
              </div>
            </Link>
          </Button>
          <Menu isOpen={getOpenState()} />
        </div>
        <SidebarToggle isOpen={isOpen} setIsOpen={toggleOpen} />
      </aside>
    );
    
    setHoveredSidebarContent(content);
  }, [sidebar, hydrated, isHoverModeEnabled]);
  
  // For non-hover mode, use direct rendering with the original implementation
  // Note: This version doesn't include the mouse event handlers needed for hover functionality
  const regularSidebarContent = useMemo(() => {
    if (!sidebar || !hydrated || isHoverModeEnabled) return null;
    
    const { isOpen, toggleOpen, getOpenState, settings } = sidebar;
    
    return (
      <aside
        className={cn(
          "fixed top-0 left-0 z-20 h-screen -translate-x-full lg:translate-x-0 transition-[width] ease-in-out duration-300",
          !getOpenState() ? "w-[60px] hover:shadow-lg" : "w-56",
          settings.disabled && "hidden"
        )}
      >
        <div
          className="relative h-full flex flex-col px-2 py-4 pb-10 overflow-hidden bg-background border-r transition-all duration-300 ease-in-out"
        >
          <Button
            className={cn(
              "mb-1",
              !getOpenState() ? "justify-center px-2" : "justify-center w-full"
            )}
            variant="link"
            asChild
          >
            <Link href="/dashboard" className="flex items-center">
              <span className={cn(
                "flex items-center min-w-[24px]",
                getOpenState() ? "hidden" : ""
              )}>
                <SendFluxLogo />
              </span>
              <div
                className={cn(
                  "whitespace-nowrap",
                  !getOpenState()
                    ? "opacity-0 w-0 ml-0"
                    : "opacity-100 w-auto flex items-center"
                )}
              >
                <SendFluxLogo size="small" />
                <h1 className="font-bold text-lg text-primary ml-2">SendFlux</h1>
              </div>
            </Link>
          </Button>
          <Menu isOpen={getOpenState()} />
        </div>
        <SidebarToggle isOpen={isOpen} setIsOpen={toggleOpen} />
      </aside>
    );
  }, [sidebar, hydrated, isHoverModeEnabled]);
  
  // Choose the appropriate content based on hover mode
  const finalContent = isHoverModeEnabled ? hoveredSidebarContent : regularSidebarContent;
  
  return <ClientOnly>{finalContent}</ClientOnly>;
}