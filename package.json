{"name": "sendflux", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "registry:build": "tsx ./scripts/build-registry.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.8", "@types/nprogress": "^0.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "geist": "^1.3.0", "html2canvas": "^1.4.1", "immer": "^10.1.1", "lucide-react": "^0.377.0", "next": "^15.1.6", "next-themes": "^0.4.4", "nprogress": "^0.2.0", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "recharts": "^2.13.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.1", "typescript": "^5"}}