"use client";

import Link from "next/link";
import { useState } from "react";
import { ChevronDown, LucideIcon } from "lucide-react";
import React from "react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { DropdownMenuArrow } from "@radix-ui/react-dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { usePathname } from "next/navigation";

type Submenu = {
  href: string;
  label: string;
  active?: boolean;
  submenus?: Submenu[];
  icon?: LucideIcon;
};

interface VerticalMenuButtonProps {
  icon: LucideIcon;
  label: string;
  active: boolean;
  submenus: Submenu[];
  isOpen: boolean | undefined;
}

const SubmenuItem = ({ submenu, isOpen = false }: { submenu: Submenu; isOpen?: boolean }) => {
  const pathname = usePathname();
  const hasSubmenus = submenu.submenus && submenu.submenus.length > 0;
  const Icon = submenu.icon;
  const isActive = (submenu.active === undefined && pathname === submenu.href) || submenu.active;
  const [isHovered, setIsHovered] = useState(false);
  const [isSubmenuOpen, setIsSubmenuOpen] = useState(false);

  if (hasSubmenus) {
    return (
      <div className="w-full">
        <Collapsible open={isSubmenuOpen} onOpenChange={setIsSubmenuOpen}>
          <CollapsibleTrigger className="w-full" asChild>
            <div 
              className="relative w-full cursor-pointer hover:bg-secondary/50 transition-colors duration-150 py-1.5 px-2 rounded-md flex items-center justify-between"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <div className="flex items-center">
                {Icon && <Icon className={cn("mr-2 h-4 w-4", isHovered && "text-primary")} />}
                <span className={cn("text-sm font-medium tracking-wide", isHovered && "font-semibold")}>{submenu.label}</span>
              </div>
              <ChevronDown 
                className={cn(
                  "h-3.5 w-3.5 opacity-70 transition-transform duration-200", 
                  isSubmenuOpen ? "transform rotate-180" : ""
                )} 
              />
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="overflow-hidden data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down pl-4 pt-1">
            {submenu.submenus?.map((subItem, subIndex) => (
              <DropdownMenuItem 
                key={subIndex} 
                asChild
                className="hover:bg-secondary/50 transition-colors duration-150 focus:bg-secondary/50 mb-1"
              >
                <Link
                  className={cn(
                    "cursor-pointer flex items-center w-full px-2 py-1.5 rounded-md",
                    ((subItem.active === undefined && pathname === subItem.href) || subItem.active) &&
                    "bg-secondary"
                  )}
                  href={subItem.href}
                >
                  <p className="max-w-[180px] truncate text-sm font-light tracking-wide">{subItem.label}</p>
                </Link>
              </DropdownMenuItem>
            ))}
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  }

  return (
    <DropdownMenuItem 
      asChild
      className="hover:bg-secondary/50 transition-colors duration-150 focus:bg-secondary/50"
    >
      <Link
        className={cn(
          "cursor-pointer",
          isActive && "bg-secondary"
        )}
        href={submenu.href}
      >
        <div className="flex items-center w-full">
          {Icon && <Icon className="mr-2 h-4 w-4" />}
          <p className="max-w-[180px] truncate text-sm font-light tracking-wide">{submenu.label}</p>
        </div>
      </Link>
    </DropdownMenuItem>
  );
};

const CollapsibleSubmenuItem = ({ submenu }: { submenu: Submenu }) => {
  const pathname = usePathname();
  const hasSubmenus = submenu.submenus && submenu.submenus.length > 0;
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const Icon = submenu.icon;

  if (hasSubmenus) {
    return (
      <Collapsible open={isCollapsed} onOpenChange={setIsCollapsed}>
        <CollapsibleTrigger className="w-full" asChild>
          <Button
            variant="ghost"
            className="w-full justify-start h-8 mb-0.5 pl-6 hover:bg-secondary/50 transition-colors duration-150 group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div className="w-full items-center flex justify-between">
              <div className="flex items-center">
                {Icon && <Icon className={cn("mr-2 h-3.5 w-3.5", isHovered && "text-primary")} />}
                <p className={cn("max-w-[220px] truncate text-sm font-medium tracking-wide", isHovered && "text-primary/90")}>{submenu.label}</p>
              </div>
              <ChevronDown
                size={18}
                className={cn("transition-transform duration-200", 
                  isCollapsed ? "rotate-180" : "",
                  isHovered && "text-primary"
                )}
              />
            </div>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="overflow-hidden data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
          {submenu.submenus?.map((subItem, subIndex) => (
            <Button
              key={subIndex}
              variant={
                (subItem.active === undefined && pathname === subItem.href) || subItem.active
                  ? "secondary"
                  : "ghost"
              }
              className="w-full justify-start h-8 mb-0.5 pl-12 hover:bg-secondary/50 transition-all duration-150"
              asChild
            >
              <Link href={subItem.href}>
                <p className="max-w-[180px] truncate text-sm font-light tracking-wide">{subItem.label}</p>
              </Link>
            </Button>
          ))}
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <Button
      variant={
        (submenu.active === undefined && pathname === submenu.href) || submenu.active
          ? "secondary"
          : "ghost"
      }
      className="w-full justify-start h-8 mb-0.5 pl-6 hover:bg-secondary/50 transition-colors duration-150"
      asChild
    >
      <Link href={submenu.href}>
        <div className="flex items-center">
          {Icon && <Icon className="mr-2 h-3.5 w-3.5" />}
          <p className="max-w-[220px] truncate text-sm font-light tracking-wide">{submenu.label}</p>
        </div>
      </Link>
    </Button>
  );
};

export function VerticalMenuButton({
  icon: Icon,
  label,
  active,
  submenus,
  isOpen
}: VerticalMenuButtonProps) {
  const pathname = usePathname();
  const isSubmenuActive = submenus.some((submenu) => {
    if (submenu.submenus) {
      return submenu.submenus.some((subItem) =>
        subItem.active === undefined ? subItem.href === pathname : subItem.active
      );
    }
    return submenu.active === undefined ? submenu.href === pathname : submenu.active;
  });
  const [isCollapsed, setIsCollapsed] = useState<boolean>(isSubmenuActive);

  return isOpen ? (
    <Collapsible
      open={isCollapsed}
      onOpenChange={setIsCollapsed}
      className="w-full"
    >
      <CollapsibleTrigger
        className="[&[data-state=open]>div>div>svg]:rotate-180 mb-1 w-full"
        asChild
      >
        <Button
          variant={isSubmenuActive ? "secondary" : "ghost"}
          className="w-full justify-start h-8 hover:bg-secondary/50 transition-colors duration-150 group"
        >
          <div className="w-full items-center flex justify-between">
            <div className="flex items-center">
              <span className={cn(
                "group-hover:text-primary transition-colors duration-150",
                isOpen ? "mr-3" : "mr-3"
              )}>
                <Icon size={16} />
              </span>
              <p
                className={cn(
                  "max-w-[220px] truncate text-sm font-medium tracking-wide group-hover:text-primary/90 transition-colors duration-150",
                  isOpen
                    ? "translate-x-0 opacity-100"
                    : "-translate-x-96 opacity-0"
                )}
              >
                {label}
              </p>
            </div>
            <div
              className={cn(
                "whitespace-nowrap",
                isOpen
                  ? "translate-x-0 opacity-100"
                  : "-translate-x-96 opacity-0"
              )}
            >
              <ChevronDown
                size={18}
                className="transition-transform duration-200 group-hover:text-primary"
              />
            </div>
          </div>
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="overflow-hidden data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down pl-2">
        {submenus.map((submenu, index) => (
          <CollapsibleSubmenuItem key={index} submenu={submenu} />
        ))}
      </CollapsibleContent>
    </Collapsible>
  ) : (
    <DropdownMenu>
      <TooltipProvider disableHoverableContent>
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <div>
                <Button
                  variant={isSubmenuActive ? "secondary" : "ghost"}
                  className={cn(
                    "relative transition-all duration-300 mb-1.5 cursor-pointer group",
                    isOpen === false ? "w-9 h-9 rounded-full p-0 hover:bg-secondary hover:shadow-sm" : "w-full h-8 justify-start"
                  )}
                >
                  <div className={cn(
                    "flex items-center",
                    isOpen === false ? "justify-center" : "w-full justify-between"
                  )}>
                    <span className={cn(
                      "flex items-center justify-center transition-all duration-300",
                      isOpen === false ? "w-6 h-6 group-hover:text-primary" : "hidden"
                    )}>
                      <Icon size={isOpen === false ? 20 : 18} className={isOpen === false ? "text-primary" : ""} />
                    </span>
                    <p
                      className={cn(
                        "truncate transition-all duration-300",
                        isOpen === false ? "opacity-0 w-0 ml-0 absolute" : "opacity-100 w-auto max-w-[220px]"
                      )}
                    >
                      {label}
                    </p>
                    {isOpen !== false && (
                      <ChevronDown size={18} className="transition-transform duration-200" />
                    )}
                  </div>
                </Button>
              </div>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent side="right" align="start" alignOffset={2} className="font-medium">
            {label}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <DropdownMenuContent 
        side="bottom" 
        sideOffset={5} 
        align="start" 
        className="min-w-[220px] p-2 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95"
      >
        <DropdownMenuLabel className="max-w-[190px] truncate px-2 py-1.5 text-base">
          {label}
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="my-1" />
        {submenus.map((submenu, index) => (
          <SubmenuItem key={index} submenu={submenu} isOpen={isOpen} />
        ))}
        <DropdownMenuArrow className="fill-border" />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}