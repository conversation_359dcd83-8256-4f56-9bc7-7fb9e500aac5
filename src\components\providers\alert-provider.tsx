"use client"

import React, { createContext, useContext, useState, useCallback } from 'react'
import { CheckCircle2, XCircle, AlertTriangle, Info } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

type AlertType = 'success' | 'error' | 'warning' | 'info'

interface AlertContextType {
  showAlert: (message: string, type: AlertType, title?: string) => void
  showSuccess: (message: string, title?: string) => void
  showError: (message: string, title?: string) => void
  showWarning: (message: string, title?: string) => void
  showInfo: (message: string, title?: string) => void
}

interface AlertState {
  message: string
  title?: string
  show: boolean
  type: AlertType
}

const AlertContext = createContext<AlertContextType | undefined>(undefined)

const alertStyles = {
  success: {
    container: "border-green-500/30 bg-green-500/10",
    icon: "text-green-600",
    title: "text-green-700 dark:text-green-400",
    message: "text-green-600 dark:text-green-400"
  },
  error: {
    container: "border-red-500/30 bg-red-500/10",
    icon: "text-red-600",
    title: "text-red-700 dark:text-red-400",
    message: "text-red-600 dark:text-red-400"
  },
  warning: {
    container: "border-yellow-500/30 bg-yellow-500/10",
    icon: "text-yellow-600",
    title: "text-yellow-700 dark:text-yellow-400",
    message: "text-yellow-600 dark:text-yellow-400"
  },
  info: {
    container: "border-blue-500/30 bg-blue-500/10",
    icon: "text-blue-600",
    title: "text-blue-700 dark:text-blue-400",
    message: "text-blue-600 dark:text-blue-400"
  }
}

const AlertIcon = ({ type }: { type: AlertType }) => {
  const icons = {
    success: CheckCircle2,
    error: XCircle,
    warning: AlertTriangle,
    info: Info
  }
  const Icon = icons[type]
  return <Icon className={cn("h-4 w-4", alertStyles[type].icon)} />
}

export function AlertProvider({ children }: { children: React.ReactNode }) {
  const [alert, setAlert] = useState<AlertState | null>(null)

  const showAlert = useCallback((message: string, type: AlertType, title?: string) => {
    setAlert({ message, title, type, show: true })
    const timer = setTimeout(() => {
      setAlert(null)
    }, 4000)
    return () => clearTimeout(timer)
  }, [])

  const showSuccess = useCallback((message: string, title?: string) => {
    showAlert(message, 'success', title)
  }, [showAlert])

  const showError = useCallback((message: string, title?: string) => {
    showAlert(message, 'error', title)
  }, [showAlert])

  const showWarning = useCallback((message: string, title?: string) => {
    showAlert(message, 'warning', title)
  }, [showAlert])

  const showInfo = useCallback((message: string, title?: string) => {
    showAlert(message, 'info', title)
  }, [showAlert])

  const closeAlert = useCallback(() => {
    setAlert(null)
  }, [])

  return (
    <AlertContext.Provider value={{ showAlert, showSuccess, showError, showWarning, showInfo }}>
      {children}
      {alert && alert.show && (
        <div className={cn(
          "fixed bottom-4 right-4 z-50 max-w-sm w-full sm:w-[320px]",
          "animate-in fade-in slide-in-from-bottom-5 duration-500"
        )}>
          <div className={cn(
            "rounded-md border shadow-lg",
            "backdrop-blur-xl",
            alertStyles[alert.type].container
          )}>
            <div className="p-2.5">
              <div className="flex items-center gap-2">
                <div className="flex-shrink-0">
                  <AlertIcon type={alert.type} />
                </div>
                <div className="flex-1 min-w-0">
                  {alert.title && (
                    <h3 className={cn(
                      "text-xs font-medium",
                      alertStyles[alert.type].title
                    )}>
                      {alert.title}
                    </h3>
                  )}
                  <p className={cn(
                    "text-xs truncate",
                    !alert.title && "font-medium",
                    alertStyles[alert.type].message
                  )}>
                    {alert.message}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <Button
                    onClick={closeAlert}
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-5 w-5 p-0 hover:bg-background/80",
                      alertStyles[alert.type].message
                    )}
                  >
                    <XCircle className="h-3 w-3" />
                    <span className="sr-only">Close</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </AlertContext.Provider>
  )
}

export const useAlert = () => {
  const context = useContext(AlertContext)
  if (context === undefined) {
    throw new Error('useAlert must be used within an AlertProvider')
  }
  return context
} 