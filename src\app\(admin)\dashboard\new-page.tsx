"use client";

import { useState } from "react";
import Link from "next/link";
import {
  Globe,
  List,
  Briefcase,
  ShoppingBasket,
  Puzzle,
  Mail,
  Send,
  Share2,
  ThumbsDown,
  Link2,
  ShoppingBag,
  Wallet,
  BarChart3,
  LineChart,
  TrendingUp,
  Activity,
  Info,
  LucideIcon
} from "lucide-react";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AnimatedCounter } from "@/components/ui/animated-counter";
import { <PERSON><PERSON><PERSON> as <PERSON>hadc<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/line-chart";
import { Bar<PERSON><PERSON> as ShadcnBar<PERSON><PERSON> } from "@/components/ui/bar-chart";

// Stat Card component for reusability
interface StatCardProps {
  icon: LucideIcon;
  value: string | number;
  label: string;
  bgColor: string;
  iconColor: string;
  textColor: string;
  tooltip: string;
}

const StatCard = ({ 
  icon: Icon, 
  value, 
  label, 
  bgColor, 
  iconColor, 
  textColor, 
  tooltip 
}: StatCardProps) => (
  <div className={`${bgColor} rounded-lg p-4 shadow-sm border hover:shadow-md transition-all duration-200 group`}>
    <div className="flex items-center justify-between">
      <div className={`rounded-full ${iconColor} p-2 group-hover:scale-110 transition-transform duration-200`}>
        <Icon className={`h-5 w-5 ${textColor}`} />
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center cursor-help">
              <span className={`text-2xl font-bold ${textColor}`}>
                <AnimatedCounter value={value} />
              </span>
              <Info className="h-4 w-4 ml-1 text-muted-foreground opacity-70" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    <div className={`mt-2 text-sm ${textColor.replace('600', '800').replace('400', '300')}`}>{label}</div>
  </div>
);

export default function DashboardPage() {
  const [chartType, setChartType] = useState<"bar" | "line">("bar");
  
  // Helper function to handle tooltips to avoid duplicate code and type issues
  const handleTooltipMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const tooltip = e.currentTarget.querySelector('.tooltip-value') as HTMLElement;
    if (tooltip) {
      tooltip.style.opacity = '1';
      tooltip.style.left = `${e.clientX - e.currentTarget.getBoundingClientRect().left}px`;
      tooltip.style.top = `${e.clientY - e.currentTarget.getBoundingClientRect().top - 30}px`;
    }
  };

  const handleTooltipLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    const tooltip = e.currentTarget.querySelector('.tooltip-value') as HTMLElement;
    if (tooltip) {
      tooltip.style.opacity = '0';
    }
  };

  // Daily earnings data
  const dailyEarningsData = Array.from({ length: 31 }, (_, i) => ({
    day: `${i + 1}`,
    revenue: [1850, 2100, 1750, 1900, 2300, 2600, 2400, 2200, 1950, 1800, 2050, 2400, 2700, 2500, 2350, 2150, 2250, 2400, 2600, 2800, 2650, 2500, 2300, 2150, 2050, 2200, 2350, 2550, 2750, 2900, 3100][i],
  }));

  // Monthly earnings data
  const monthlyEarningsData = [
    { month: 'Jan', revenue: 38000 },
    { month: 'Feb', revenue: 42000 },
    { month: 'Mar', revenue: 48775 },
    { month: 'Apr', revenue: 0 },
    { month: 'May', revenue: 0 },
    { month: 'Jun', revenue: 0 },
    { month: 'Jul', revenue: 0 },
    { month: 'Aug', revenue: 0 },
    { month: 'Sep', revenue: 0 },
    { month: 'Oct', revenue: 0 },
    { month: 'Nov', revenue: 0 },
    { month: 'Dec', revenue: 0 }
  ].filter(item => item.revenue > 0);
  
  return (
    <ContentLayout title="Dashboard">
      {/* Dashboard Content */}
      <div className="mt-6 space-y-6">
        <div className="grid grid-cols-12 gap-4 lg:gap-6">
          {/* Overview Section - Left Panel (4 Columns) */}
          <div className="col-span-12 lg:col-span-4 space-y-6">
            <Card className="hover:shadow-md transition-all duration-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-medium">
                  <Activity className="inline-block mr-2 h-5 w-5" />
                  Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  {/* Active Servers */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 shadow-sm border border-blue-100 dark:border-blue-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-blue-100 dark:bg-blue-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Globe className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        <AnimatedCounter value="24" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-blue-800 dark:text-blue-300">Active Servers</div>
                  </div>

                  {/* Active IPs */}
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 shadow-sm border border-purple-100 dark:border-purple-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-purple-100 dark:bg-purple-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <List className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <span className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        <AnimatedCounter value="186" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-purple-800 dark:text-purple-300">Active IPs</div>
                  </div>

                  {/* Affiliate Networks */}
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 shadow-sm border border-green-100 dark:border-green-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-green-100 dark:bg-green-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Briefcase className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                        <AnimatedCounter value="12" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-green-800 dark:text-green-300">Affiliate Networks</div>
                  </div>

                  {/* Active Offers */}
                  <div className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4 shadow-sm border border-amber-100 dark:border-amber-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-amber-100 dark:bg-amber-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <ShoppingBasket className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                      </div>
                      <span className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                        <AnimatedCounter value="38" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-amber-800 dark:text-amber-300">Active Offers</div>
                  </div>

                  {/* Daily Tests */}
                  <div className="bg-cyan-50 dark:bg-cyan-900/20 rounded-lg p-4 shadow-sm border border-cyan-100 dark:border-cyan-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-cyan-100 dark:bg-cyan-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Puzzle className="h-5 w-5 text-cyan-600 dark:text-cyan-400" />
                      </div>
                      <span className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
                        <AnimatedCounter value="156" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-cyan-800 dark:text-cyan-300">Daily Tests</div>
                  </div>

                  {/* Daily Drops */}
                  <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 shadow-sm border border-indigo-100 dark:border-indigo-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-indigo-100 dark:bg-indigo-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Mail className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <span className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                        <AnimatedCounter value="32" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-indigo-800 dark:text-indigo-300">Daily Drops</div>
                  </div>

                  {/* Daily Sent */}
                  <div className="bg-teal-50 dark:bg-teal-900/20 rounded-lg p-4 shadow-sm border border-teal-100 dark:border-teal-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-teal-100 dark:bg-teal-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Send className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                      </div>
                      <span className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                        <AnimatedCounter value="1.2M" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-teal-800 dark:text-teal-300">Daily Sent</div>
                  </div>

                  {/* Daily Delivered */}
                  <div className="bg-emerald-50 dark:bg-emerald-900/20 rounded-lg p-4 shadow-sm border border-emerald-100 dark:border-emerald-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-emerald-100 dark:bg-emerald-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Share2 className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <span className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                        <AnimatedCounter value="956K" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-emerald-800 dark:text-emerald-300">Daily Delivered</div>
                  </div>

                  {/* Daily Hard Bounced */}
                  <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 shadow-sm border border-red-100 dark:border-red-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-red-100 dark:bg-red-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <ThumbsDown className="h-5 w-5 text-red-600 dark:text-red-400" />
                      </div>
                      <span className="text-2xl font-bold text-red-600 dark:text-red-400">
                        <AnimatedCounter value="244K" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-red-800 dark:text-red-300">Daily Hard Bounced</div>
                  </div>

                  {/* Monthly Clicks */}
                  <div className="bg-fuchsia-50 dark:bg-fuchsia-900/20 rounded-lg p-4 shadow-sm border border-fuchsia-100 dark:border-fuchsia-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-fuchsia-100 dark:bg-fuchsia-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Link2 className="h-5 w-5 text-fuchsia-600 dark:text-fuchsia-400" />
                      </div>
                      <span className="text-2xl font-bold text-fuchsia-600 dark:text-fuchsia-400">
                        <AnimatedCounter value="98.6K" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-fuchsia-800 dark:text-fuchsia-300">Monthly Clicks</div>
                  </div>

                  {/* Monthly Leads */}
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 shadow-sm border border-yellow-100 dark:border-yellow-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-yellow-100 dark:bg-yellow-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <ShoppingBag className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                      </div>
                      <span className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                        <AnimatedCounter value="12.4K" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-yellow-800 dark:text-yellow-300">Monthly Leads</div>
                  </div>

                  {/* Monthly Earnings */}
                  <div className="bg-lime-50 dark:bg-lime-900/20 rounded-lg p-4 shadow-sm border border-lime-100 dark:border-lime-900/30 hover:shadow-md transition-all duration-200 group">
                    <div className="flex items-center justify-between">
                      <div className="rounded-full bg-lime-100 dark:bg-lime-800 p-2 group-hover:scale-110 transition-transform duration-200">
                        <Wallet className="h-5 w-5 text-lime-600 dark:text-lime-400" />
                      </div>
                      <span className="text-2xl font-bold text-lime-600 dark:text-lime-400">
                        <AnimatedCounter value="$48.5K" />
                      </span>
                    </div>
                    <div className="mt-2 text-sm text-lime-800 dark:text-lime-300">Monthly Earnings</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Charts - Right Panel (8 Columns) */}
          <div className="col-span-12 lg:col-span-8 space-y-6">
            <Card className="hover:shadow-md transition-all duration-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-medium">
                  <BarChart3 className="inline-block mr-2 h-5 w-5" />
                  Performance Charts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Chart 1: Delivery vs. Bounced */}
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <h3 className="text-sm font-medium mb-4">Delivery vs. Bounced (March 2025)</h3>
                    <div className="h-64 flex items-end space-x-2 sm:space-x-4">
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-green-500 h-[70%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">70%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Delivered</div>
                      </div>
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-amber-400 h-[15%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">15%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Soft Bounce</div>
                      </div>
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-red-500 h-[10%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">10%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Hard Bounce</div>
                      </div>
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-gray-300 h-[5%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">5%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Blocked</div>
                      </div>
                    </div>
                  </div>

                  {/* Chart 2: Opens/Clicks/Leads/Unsubs */}
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <h3 className="text-sm font-medium mb-4">Engagement (March 2025)</h3>
                    <div className="h-64 flex items-end space-x-2 sm:space-x-4">
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-blue-500 h-[35%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">35%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Opens</div>
                      </div>
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-purple-500 h-[12%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">12%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Clicks</div>
                      </div>
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-teal-500 h-[8%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">8%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Leads</div>
                      </div>
                      <div className="relative h-full flex-1 flex flex-col justify-end group">
                        <div className="bg-red-400 h-[3%] rounded-t-sm group-hover:opacity-90 transition-opacity"></div>
                        <div className="absolute -top-7 left-1/2 -translate-x-1/2 text-[10px] sm:text-xs font-medium">3%</div>
                        <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Unsubs</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Status/Performance Card */}
            <Card className="hover:shadow-md transition-all duration-200">
              <CardHeader className="pb-2">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <CardTitle className="text-xl font-medium">
                    <Activity className="inline-block mr-2 h-5 w-5" />
                    Campaign Status
                  </CardTitle>
                  <Tabs defaultValue="today" className="w-full sm:w-[300px]">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="today">Today</TabsTrigger>
                      <TabsTrigger value="week">This Week</TabsTrigger>
                      <TabsTrigger value="month">This Month</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">Delivery Rate</p>
                      <span className="text-sm bg-green-100 text-green-800 py-0.5 px-2 rounded-full">Good</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="92.8%" />
                    </p>
                    <p className="text-xs text-green-600 mt-1">↑ 1.2% from yesterday</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">Open Rate</p>
                      <span className="text-sm bg-amber-100 text-amber-800 py-0.5 px-2 rounded-full">Average</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="34.6%" />
                    </p>
                    <p className="text-xs text-red-600 mt-1">↓ 0.3% from yesterday</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">CTR</p>
                      <span className="text-sm bg-green-100 text-green-800 py-0.5 px-2 rounded-full">Good</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="12.4%" />
                    </p>
                    <p className="text-xs text-green-600 mt-1">↑ 0.8% from yesterday</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">Conv. Rate</p>
                      <span className="text-sm bg-blue-100 text-blue-800 py-0.5 px-2 rounded-full">Excellent</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="8.9%" />
                    </p>
                    <p className="text-xs text-green-600 mt-1">↑ 1.6% from yesterday</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Earnings Charts */}
        <div className="mt-8">
          <Card className="hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <CardTitle className="text-xl font-medium">
                  <LineChart className="inline-block mr-2 h-5 w-5" />
                  Earnings Analytics
                </CardTitle>
                <Tabs 
                  defaultValue="bar" 
                  value={chartType} 
                  onValueChange={(value) => setChartType(value as "bar" | "line")} 
                  className="w-[140px]"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="bar">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Bar
                    </TabsTrigger>
                    <TabsTrigger value="line">
                      <LineChart className="h-4 w-4 mr-1" />
                      Line
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Daily Earnings (March 2025) */}
                <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <h3 className="text-sm font-medium mb-4">Daily Earnings (March 2025)</h3>
                  {chartType === "bar" ? (
                    <ShadcnBarChart
                      data={dailyEarningsData}
                      categories={["revenue"]}
                      index="day"
                      colors={["#84cc16"]}
                      valueFormatter={(value) => `$${value.toLocaleString()}`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  ) : (
                    <ShadcnLineChart
                      data={dailyEarningsData}
                      categories={["revenue"]}
                      index="day"
                      colors={["#84cc16"]}
                      valueFormatter={(value) => `$${value.toLocaleString()}`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  )}
                </div>

                {/* Monthly Earnings (2025) */}
                <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <h3 className="text-sm font-medium mb-4">Monthly Earnings (2025)</h3>
                  {chartType === "bar" ? (
                    <ShadcnBarChart
                      data={monthlyEarningsData}
                      categories={["revenue"]}
                      index="month"
                      colors={["#3b82f6"]}
                      valueFormatter={(value) => `$${(value / 1000).toFixed(1)}K`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  ) : (
                    <ShadcnLineChart
                      data={monthlyEarningsData}
                      categories={["revenue"]}
                      index="month"
                      colors={["#3b82f6"]}
                      valueFormatter={(value) => `$${(value / 1000).toFixed(1)}K`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ContentLayout>
  );
} 