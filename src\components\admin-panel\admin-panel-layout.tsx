"use client";

import { Sidebar } from "@/components/admin-panel/sidebar";
import { useSidebar } from "@/hooks/use-sidebar";
import { useStore } from "@/hooks/use-store";
import { cn } from "@/lib/utils";
import { ClientOnly } from "@/components/client-only";
import { useHydratedSidebar } from "@/hooks/use-sidebar";

export default function AdminPanelLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const { hydrated } = useHydratedSidebar();
  const sidebar = useStore(useSidebar, (x) => x);
  
  // Return a loading state or null while the store is initializing
  if (!hydrated || !sidebar) {
    return null;
  }

  const { getOpenState, settings } = sidebar;

  const layoutContent = (
    <>
      <Sidebar />
      <main
        className={cn(
          "min-h-[calc(100vh)] bg-zinc-50 dark:bg-zinc-900 transition-[margin-left] ease-in-out duration-300",
          !settings?.disabled && (!getOpenState() ? "lg:ml-[60px]" : "lg:ml-56")
        )}
      >
        {children}
      </main>
    </>
  );

  return <ClientOnly>{layoutContent}</ClientOnly>;
}
