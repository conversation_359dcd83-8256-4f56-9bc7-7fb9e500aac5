"use client";

import { useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import NProgress from "nprogress";
import { cn } from "@/lib/utils";

type RouteProgressProps = {
  color?: string;
  height?: number;
  showSpinner?: boolean;
  startPosition?: number;
  stopDelayMs?: number;
  options?: Partial<NProgress.NProgressOptions>;
};

// You'll need to add NProgress styles to your globals.css or create a separate CSS file
export function RouteProgress({
  color = "#0091FF",
  height = 2,
  showSpinner = false,
  startPosition = 0.3,
  stopDelayMs = 200,
  options = { showSpinner: false },
}: RouteProgressProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [prevPath, setPrevPath] = useState("");

  useEffect(() => {
    // Add NProgress CSS
    const style = document.createElement("style");
    style.textContent = `
      #nprogress {
        pointer-events: none;
      }
      
      #nprogress .bar {
        background: ${color};
        position: fixed;
        z-index: 1031;
        top: 0;
        left: 0;
        width: 100%;
        height: ${height}px;
      }
      
      #nprogress .peg {
        display: block;
        position: absolute;
        right: 0px;
        width: 100px;
        height: 100%;
        box-shadow: 0 0 10px ${color}, 0 0 5px ${color};
        opacity: 1.0;
        -webkit-transform: rotate(3deg) translate(0px, -4px);
        -ms-transform: rotate(3deg) translate(0px, -4px);
        transform: rotate(3deg) translate(0px, -4px);
      }
      
      #nprogress .spinner {
        display: ${showSpinner ? "block" : "none"};
        position: fixed;
        z-index: 1031;
        top: 15px;
        right: 15px;
      }
      
      #nprogress .spinner-icon {
        width: 18px;
        height: 18px;
        box-sizing: border-box;
        
        border: solid 2px transparent;
        border-top-color: ${color};
        border-left-color: ${color};
        border-radius: 50%;
        
        -webkit-animation: nprogress-spinner 400ms linear infinite;
        animation: nprogress-spinner 400ms linear infinite;
      }
      
      @-webkit-keyframes nprogress-spinner {
        0%   { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
      }
      @keyframes nprogress-spinner {
        0%   { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    NProgress.configure({
      showSpinner,
      trickleSpeed: 100,
      ...options,
    });

    return () => {
      document.head.removeChild(style);
    };
  }, [color, height, showSpinner, options]);

  useEffect(() => {
    const currentPath = pathname + searchParams.toString();

    if (currentPath !== prevPath) {
      NProgress.set(startPosition);
      NProgress.start();
      setPrevPath(currentPath);
    }

    const timer = setTimeout(() => {
      NProgress.done(true);
    }, stopDelayMs);

    return () => {
      clearTimeout(timer);
    };
  }, [pathname, searchParams, prevPath, startPosition, stopDelayMs]);

  return null;
} 