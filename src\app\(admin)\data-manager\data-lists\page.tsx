"use client";

import Link from "next/link";
import { useState, use<PERSON><PERSON>back } from "react";
import { ChevronDown, ChevronUp, MoreVertical, Clock, Eye, MousePointer, Ban, UserPlus, Globe2, AlertTriangle, Shield, XCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

import PlaceholderContent from "@/components/demo/placeholder-content";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  <PERSON>readcrumbI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>crumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";
import { cn } from "@/lib/utils";
import { getCountryName } from "@/lib/countries";

export default function DataListsPage() {
  // Data for email lists
  interface EmailListItem {
    id: number;
    name: string;
    geo: string;
    isp: string;
    provider: string;
    fresh: number;
    clean: number;
    openers: number;
    clickers: number;
    unsub: number;
    lead: number;
    bounce: number;
    hardBounce: number;
    otherGeo: number;
    status: string;
  }

  const [emailLists, setEmailLists] = useState<EmailListItem[]>([
    { id: 1, name: 'list_usa_premium', geo: 'us', isp: 'Gmail', provider: 'e-impact', fresh: 45000, clean: 42500, openers: 8500, clickers: 2100, unsub: 320, lead: 85, bounce: 450, hardBounce: 150, otherGeo: 15, status: 'active' },
    { id: 2, name: 'uk_finance_leads', geo: 'gb', isp: 'Hotmail', provider: 'infobase', fresh: 32000, clean: 30800, openers: 6200, clickers: 1800, unsub: 240, lead: 65, bounce: 320, hardBounce: 95, otherGeo: 10, status: 'active' },
    { id: 3, name: 'ca_subscribers', geo: 'ca', isp: 'Yahoo', provider: 'e-impact', fresh: 28500, clean: 27100, openers: 5400, clickers: 1600, unsub: 180, lead: 55, bounce: 290, hardBounce: 80, otherGeo: 5, status: 'active' },
    { id: 4, name: 'au_newsletters', geo: 'au', isp: 'Outlook', provider: 'datamax', fresh: 18000, clean: 17200, openers: 3400, clickers: 950, unsub: 120, lead: 30, bounce: 180, hardBounce: 45, otherGeo: 8, status: 'paused' },
    { id: 5, name: 'de_business', geo: 'de', isp: 'GMX', provider: 'leadgen', fresh: 35000, clean: 33250, openers: 6600, clickers: 1950, unsub: 275, lead: 70, bounce: 350, hardBounce: 105, otherGeo: 12, status: 'active' },
    { id: 6, name: 'fr_shoppers', geo: 'fr', isp: 'Orange', provider: 'e-impact', fresh: 26000, clean: 24700, openers: 4900, clickers: 1450, unsub: 210, lead: 60, bounce: 260, hardBounce: 75, otherGeo: 9, status: 'active' },
    { id: 7, name: 'es_technology', geo: 'es', isp: 'Gmail', provider: 'datamax', fresh: 22000, clean: 20900, openers: 4200, clickers: 1250, unsub: 180, lead: 45, bounce: 220, hardBounce: 60, otherGeo: 7, status: 'paused' },
    { id: 8, name: 'it_fashion', geo: 'it', isp: 'Libero', provider: 'infobase', fresh: 19500, clean: 18500, openers: 3700, clickers: 1100, unsub: 160, lead: 40, bounce: 195, hardBounce: 55, otherGeo: 6, status: 'active' },
    { id: 9, name: 'nl_finance', geo: 'nl', isp: 'KPN', provider: 'leadgen', fresh: 17000, clean: 16150, openers: 3200, clickers: 950, unsub: 140, lead: 35, bounce: 170, hardBounce: 50, otherGeo: 5, status: 'active' },
    { id: 10, name: 'cn_ecommerce', geo: 'cn', isp: '163.com', provider: 'e-impact', fresh: 42000, clean: 39900, openers: 8000, clickers: 2350, unsub: 300, lead: 80, bounce: 420, hardBounce: 125, otherGeo: 14, status: 'active' },
  ]);

  // Sorting functionality
  const [sortConfig, setSortConfig] = useState<{
    key: keyof EmailListItem | null;
    direction: 'asc' | 'desc' | null;
  }>({
    key: null,
    direction: null
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Sorting function for data items
  const sortData = useCallback((key: keyof EmailListItem) => {
    let direction: 'asc' | 'desc' | null = 'asc';
    
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        direction = 'desc';
      } else if (sortConfig.direction === 'desc') {
        direction = null;
      }
    }
    
    setSortConfig({ key, direction });
  }, [sortConfig]);

  // Get sorted and paginated data
  const getSortedData = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    let sortedItems = [...emailLists];
    
    if (sortConfig.key && sortConfig.direction) {
      sortedItems.sort((a, b) => {
        if (a[sortConfig.key!] < b[sortConfig.key!]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key!] > b[sortConfig.key!]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    
    return sortedItems.slice(startIndex, endIndex);
  }, [emailLists, sortConfig, currentPage]);

  // Function to get sorting icon for headers
  const getSortIcon = useCallback((key: keyof EmailListItem) => {
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        return <ChevronUp className="h-3 w-3 text-primary" />;
      } else if (sortConfig.direction === 'desc') {
        return <ChevronDown className="h-3 w-3 text-primary" />;
      }
    }
    return (
      <div className="flex flex-col ml-1">
        <ChevronUp className="h-3 w-3" />
        <ChevronDown className="h-3 w-3 -mt-1" />
      </div>
    );
  }, [sortConfig]);

  // Pagination functions
  const totalPages = Math.ceil(emailLists.length / itemsPerPage);
  
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Country flag component
  const CountryFlag = ({ country }: { country: string }) => {
    return (
      <div className="flex items-center justify-center">
        <img
          src={`https://flagcdn.com/w20/${country.toLowerCase()}.png`}
          className="h-4 w-6 object-cover"
          alt={getCountryName(country)}
          title={getCountryName(country)}
        />
      </div>
    );
  };

  return (
    <ContentLayout title="Data Lists">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/data-manager">Data Manager</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Data Lists</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Email Lists Card Table */}
      <Card className="mt-4">
        <CardHeader className="flex flex-row items-center justify-between py-3 px-5">
          <CardTitle className="text-base font-medium">Email Lists</CardTitle>
          <div className="flex space-x-2">
            <Button 
              size="sm" 
              className="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-8 rounded-md px-3 text-xs bg-green-600 hover:bg-green-700 text-white"
            >
              Upload Lists
            </Button>
            <Button 
              size="sm" 
              className="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 shadow h-8 rounded-md px-3 text-xs bg-purple-600 hover:bg-purple-700 text-white"
            >
              Add New List
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="rounded-md">
            <div className="overflow-auto border rounded-md">
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="hover:bg-muted/50">
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('name')}>
                      <div className="flex items-center">
                        NAME
                        {getSortIcon('name')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('geo')}>
                      <div className="flex items-center">
                        GEO
                        {getSortIcon('geo')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('isp')}>
                      <div className="flex items-center">
                        ISP
                        {getSortIcon('isp')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('provider')}>
                      <div className="flex items-center">
                        PROVIDER
                        {getSortIcon('provider')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('fresh')}>
                      <div className="flex items-center">
                        FRESH
                        {getSortIcon('fresh')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('clean')}>
                      <div className="flex items-center">
                        <div className="flex items-center">
                          <Shield className="h-3 w-3 mr-1 text-green-500" />
                          CLEAN
                        </div>
                        {getSortIcon('clean')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('openers')}>
                      <div className="flex items-center">
                        OPENERS
                        {getSortIcon('openers')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('clickers')}>
                      <div className="flex items-center">
                        CLICKERS
                        {getSortIcon('clickers')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('unsub')}>
                      <div className="flex items-center">
                        UNSUB
                        {getSortIcon('unsub')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('lead')}>
                      <div className="flex items-center">
                        LEAD
                        {getSortIcon('lead')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('bounce')}>
                      <div className="flex items-center">
                        <div className="flex items-center">
                          <XCircle className="h-3 w-3 mr-1 text-red-500" />
                          BOUNCE
                        </div>
                        {getSortIcon('bounce')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('hardBounce')}>
                      <div className="flex items-center">
                        HARD-BOUNCE
                        {getSortIcon('hardBounce')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('otherGeo')}>
                      <div className="flex items-center">
                        OTHER GEO
                        {getSortIcon('otherGeo')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('status')}>
                      <div className="flex items-center">
                        STATUS
                        {getSortIcon('status')}
                      </div>
                    </TableHead>
                    <TableHead className="font-medium text-muted-foreground">
                      <div className="flex items-center">
                        ACTION
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getSortedData().map((item) => (
                    <TableRow key={item.id} className="border-b transition-colors hover:bg-muted/50">
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>
                        <CountryFlag country={item.geo} />
                      </TableCell>
                      <TableCell>{item.isp}</TableCell>
                      <TableCell>{item.provider}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-teal-500" />
                          {item.fresh.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Shield className="h-3 w-3 mr-1 text-green-500" />
                          {item.clean.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Eye className="h-3 w-3 mr-1 text-blue-500" />
                          {item.openers.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MousePointer className="h-3 w-3 mr-1 text-purple-500" />
                          {item.clickers.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Ban className="h-3 w-3 mr-1 text-red-500" />
                          {item.unsub.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <UserPlus className="h-3 w-3 mr-1 text-green-500" />
                          {item.lead.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <XCircle className="h-3 w-3 mr-1 text-red-500" />
                          {item.bounce.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <AlertTriangle className="h-3 w-3 mr-1 text-amber-500" />
                          {item.hardBounce.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Globe2 className="h-3 w-3 mr-1 text-indigo-500" />
                          {item.otherGeo.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>Edit</DropdownMenuItem>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Export</DropdownMenuItem>
                            <DropdownMenuItem className="text-red-500">Delete</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            {/* Pagination */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, emailLists.length)} to {Math.min(currentPage * itemsPerPage, emailLists.length)} of {emailLists.length} lists
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </ContentLayout>
  );
} 