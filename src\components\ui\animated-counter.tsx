"use client";

import { useState, useEffect } from "react";

interface AnimatedCounterProps {
  value: string | number;
  suffix?: string;
  duration?: number;
}

export function AnimatedCounter({ 
  value, 
  suffix = "", 
  duration = 1000 
}: AnimatedCounterProps) {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    // If value contains K, M, or similar, extract the number part
    let targetValue = value;
    let unit = "";
    
    if (typeof value === 'string') {
      // Handle values like "1.2M", "24K", "92.8%"
      const match = value.match(/^([\d.]+)([KMB]|%)?$/);
      if (match) {
        targetValue = parseFloat(match[1]);
        unit = match[2] || "";
      } 
      // Handle currency values like "$48.5K"
      else if (value.startsWith('$')) {
        const match = value.substring(1).match(/^([\d.]+)([KMB]|%)?$/);
        if (match) {
          targetValue = parseFloat(match[1]);
          unit = "$" + (match[2] || "");
        } else {
          targetValue = parseFloat(value.substring(1));
          unit = "$";
        }
      }
    }
    
    let start = 0;
    const end = Number(targetValue);
    const increment = end / (duration / 16); // 60fps
    
    const timer = setInterval(() => {
      start += increment;
      if (start >= end) {
        setCount(end);
        clearInterval(timer);
      } else {
        setCount(Math.floor(start));
      }
    }, 16);
    
    return () => clearInterval(timer);
  }, [value, duration]);
  
  // Format the output based on unit
  const formattedCount = () => {
    if (typeof value === 'string') {
      if (value.startsWith('$')) {
        if (value.includes('K')) {
          return `$${count}K`;
        }
        return `$${count.toLocaleString()}`;
      } else if (value.includes('K')) {
        return `${count}K`;
      } else if (value.includes('M')) {
        return `${count}M`;
      } else if (value.includes('%')) {
        return `${count.toFixed(1)}%`;
      }
    }
    return `${count.toLocaleString()}${suffix}`;
  };
  
  return <span className="tabular-nums">{formattedCount()}</span>;
} 