"use client";

import { useState } from "react";
import {
  Line<PERSON>hart as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import { cn } from "@/lib/utils";

interface LineChartProps {
  data: Array<Record<string, any>>;
  categories: string[];
  index: string;
  colors?: string[];
  valueFormatter?: (value: number) => string;
  yAxisWidth?: number;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  showXAxis?: boolean;
  showYAxis?: boolean;
  className?: string;
}

export function LineChart({
  data,
  categories,
  index,
  colors = ["#3b82f6", "#10b981", "#6366f1", "#f97316", "#8b5cf6"],
  valueFormatter = (value: number) => `${value}`,
  yAxisWidth = 40,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  showXAxis = true,
  showYAxis = true,
  className,
}: LineChartProps) {
  const [focusBar, setFocusBar] = useState<string | null>(null);
  const [mouseLeave, setMouseLeave] = useState(true);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm text-xs">
          <div className="font-medium">{label}</div>
          <div className="mt-1 flex flex-col gap-0.5">
            {payload.map((item: any, idx: number) => (
              <div key={idx} className="flex items-center gap-2">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <span className="font-medium text-muted-foreground">
                  {item.name}:
                </span>
                <span className="font-medium">
                  {valueFormatter(item.value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  const handleMouseEvent = (e: any) => {
    if (e.activePayload) {
      setFocusBar(e.activeLabel);
      setMouseLeave(false);
    }
  };

  return (
    <div className={cn("h-80 w-full pt-4", className)}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart
          data={data}
          margin={{
            top: 16,
            right: 16,
            left: 0,
            bottom: 0,
          }}
          onMouseMove={handleMouseEvent}
          onMouseLeave={() => setMouseLeave(true)}
        >
          {showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              className="stroke-muted"
            />
          )}
          {showXAxis && (
            <XAxis
              dataKey={index}
              tick={{ transform: "translate(0, 6)" }}
              ticks={data.map((item) => item[index])}
              tickLine={false}
              axisLine={false}
              className="text-xs fill-muted-foreground"
              padding={{ left: 8, right: 8 }}
            />
          )}
          {showYAxis && (
            <YAxis
              width={yAxisWidth}
              axisLine={false}
              tickLine={false}
              className="text-xs fill-muted-foreground"
              tickFormatter={valueFormatter}
            />
          )}
          {showTooltip && <Tooltip content={<CustomTooltip />} wrapperStyle={{ outline: "none" }} />}
          {showLegend && (
            <Legend
              verticalAlign="top"
              height={40}
              iconType="circle"
              iconSize={8}
              className="text-xs fill-muted-foreground"
            />
          )}

          {categories.map((category, i) => (
            <Line
              key={category}
              type="monotone"
              dataKey={category}
              activeDot={{ r: 6 }}
              stroke={colors[i % colors.length]}
              strokeWidth={2}
              dot={{ r: 4 }}
              className={cn(
                focusBar && mouseLeave === false
                  ? "opacity-30"
                  : "opacity-100",
                "transition-opacity duration-200"
              )}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
} 