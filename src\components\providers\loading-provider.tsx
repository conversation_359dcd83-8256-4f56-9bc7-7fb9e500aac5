"use client";

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from "react";
import { PageLoader } from "@/components/page-loader";

type LoadingVariant = "default" | "primary" | "secondary" | "destructive" | "accent";
type LoadingAnimation = "spin" | "pulse" | "bounce";

interface LoadingOptions {
  message?: string;
  variant?: LoadingVariant;
  animation?: LoadingAnimation;
  showProgressBar?: boolean;
  showProgressInSpinner?: boolean;
  blurBackground?: boolean;
}

interface LoadingContextType {
  isLoading: boolean;
  progress: number | null;
  startLoading: (optionsOrMessage?: LoadingOptions | string) => void;
  stopLoading: () => void;
  updateProgress: (progress: number) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function useLoading() {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }
  return context;
}

interface LoadingProviderProps {
  children: React.ReactNode;
  defaultOptions?: LoadingOptions;
}

export function LoadingProvider({ 
  children,
  defaultOptions = {
    variant: "primary",
    animation: "spin",
    showProgressBar: true,
    blurBackground: true
  } 
}: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState<number | null>(null);
  const [options, setOptions] = useState<LoadingOptions>(defaultOptions);
  
  // For simulating progress when no real progress is provided
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isAutomaticProgressRef = useRef(false);

  const startLoading = useCallback((optionsOrMessage?: LoadingOptions | string) => {
    let newOptions = { ...defaultOptions };
    
    if (typeof optionsOrMessage === 'string') {
      newOptions.message = optionsOrMessage;
    } else if (optionsOrMessage) {
      newOptions = { ...defaultOptions, ...optionsOrMessage };
    }
    
    setOptions(newOptions);
    setIsLoading(true);
    
    // If no explicit progress will be provided, simulate it
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
    
    // Start with a small progress to show something immediately
    setProgress(5);
    isAutomaticProgressRef.current = true;
    
    progressIntervalRef.current = setInterval(() => {
      setProgress(prev => {
        if (!prev) return 5;
        
        // Slow down as we approach 90%
        if (prev < 30) return prev + 3;
        if (prev < 50) return prev + 2;
        if (prev < 70) return prev + 1;
        if (prev < 90) return prev + 0.5;
        return prev;
      });
    }, 700);
    
  }, [defaultOptions]);

  const updateProgress = useCallback((newProgress: number) => {
    // If we're manually updating progress, stop any automatic updates
    if (progressIntervalRef.current && isAutomaticProgressRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
      isAutomaticProgressRef.current = false;
    }
    
    setProgress(Math.max(0, Math.min(100, newProgress)));
  }, []);

  const stopLoading = useCallback(() => {
    // When stopping, quickly complete the progress bar
    if (isAutomaticProgressRef.current || progress !== 100) {
      setProgress(100);
      
      // Short delay before hiding to show the completed progress
      setTimeout(() => {
        setIsLoading(false);
        setProgress(null);
        
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current);
          progressIntervalRef.current = null;
        }
        isAutomaticProgressRef.current = false;
      }, 300);
    } else {
      setIsLoading(false);
      setProgress(null);
      
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      isAutomaticProgressRef.current = false;
    }
  }, [progress]);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  return (
    <LoadingContext.Provider value={{ isLoading, progress: progress, startLoading, stopLoading, updateProgress }}>
      {children}
      {isLoading && (
        <PageLoader 
          fullScreen 
          label={options.message}
          variant={options.variant}
          animation={options.animation}
          progress={progress !== null ? progress : undefined}
          showProgressBar={options.showProgressBar}
          showProgressInSpinner={options.showProgressInSpinner}
          blurBackground={options.blurBackground}
        />
      )}
    </LoadingContext.Provider>
  );
} 