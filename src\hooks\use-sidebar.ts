"use client";

import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { produce } from "immer";
import { useEffect, useState } from "react";

type SidebarSettings = { 
  disabled: boolean; 
  isHoverOpen: boolean;
  submenuHoverDelay: number;
};

type SidebarStore = {
  isOpen: boolean;
  isHover: boolean;
  settings: SidebarSettings;
  toggleOpen: () => void;
  setIsOpen: (isOpen: boolean) => void;
  setIsHover: (isHover: boolean) => void;
  getOpenState: () => boolean;
  setSettings: (settings: Partial<SidebarSettings>) => void;
};

const initialState: Pick<SidebarStore, 'isOpen' | 'isHover' | 'settings'> = {
  isOpen: true,
  isHover: false,
  settings: { 
    disabled: false, 
    isHoverOpen: false,
    submenuHoverDelay: 300
  }
};

export const useSidebar = create(
  persist<SidebarStore>(
    (set, get) => ({
      ...initialState,
      toggleOpen: () => {
        set({ isOpen: !get().isOpen });
      },
      setIsOpen: (isOpen: boolean) => {
        set({ isOpen });
      },
      setIsHover: (isHover: boolean) => {
        set({ isHover });
      },
      getOpenState: () => {
        const state = get();
        return state.isOpen;
      },
      setSettings: (settings: Partial<SidebarSettings>) => {
        set(
          produce((state: SidebarStore) => {
            state.settings = { ...state.settings, ...settings };
          })
        );
      }
    }),
    {
      name: "sidebar",
      storage: createJSONStorage(() => {
        return typeof window !== 'undefined' ? window.localStorage : undefined as any;
      }),
      skipHydration: true
    }
  )
);

// This is a React hook that helps with hydration
export function useHydratedSidebar() {
  const [hydrated, setHydrated] = useState(false);
  
  useEffect(() => {
    // This effect only runs on the client, so it's safe to access localStorage
    useSidebar.persist.rehydrate();
    setHydrated(true);
  }, []);
  
  return { hydrated };
}
