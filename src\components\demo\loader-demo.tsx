"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useLoading } from "@/hooks/use-loading";
import { Loader } from "@/components/ui/loader";
import { PageLoader } from "@/components/page-loader";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

export default function LoaderDemo() {
  const { startLoading, stopLoading, updateProgress } = useLoading();
  const [isComponentLoading, setIsComponentLoading] = useState(false);
  const [componentProgress, setComponentProgress] = useState(0);
  const [progressUpdateInterval, setProgressUpdateInterval] = useState<NodeJS.Timeout | null>(null);
  
  // Dynamic loading options state
  const [loaderVariant, setLoaderVariant] = useState<string>("primary");
  const [loaderAnimation, setLoaderAnimation] = useState<string>("spin");
  const [showProgressBar, setShowProgressBar] = useState(true);
  const [showProgressInSpinner, setShowProgressInSpinner] = useState(false);
  const [blurBackground, setBlurBackground] = useState(true);
  const [useManualProgress, setUseManualProgress] = useState(false);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (progressUpdateInterval) {
        clearInterval(progressUpdateInterval);
      }
    };
  }, [progressUpdateInterval]);

  const simulateProgressUpdate = (forComponent = false) => {
    // Reset any existing intervals
    if (progressUpdateInterval) {
      clearInterval(progressUpdateInterval);
      setProgressUpdateInterval(null);
    }
    
    // Reset progress
    if (forComponent) {
      setComponentProgress(0);
    } else {
      updateProgress(0);
    }
    
    // Create new progress simulation
    let localProgress = 0;
    
    const interval = setInterval(() => {
      if (forComponent) {
        setComponentProgress((prev: number) => {
          const newValue = prev + (Math.random() * 5 + 1);
          if (newValue >= 100) {
            clearInterval(interval);
            // Complete the loading after reaching 100%
            setTimeout(() => {
              setIsComponentLoading(false);
              setComponentProgress(0);
            }, 500);
            return 100;
          }
          return newValue;
        });
      } else {
        // Increment progress manually
        localProgress += (Math.random() * 5 + 1);
        
        if (localProgress >= 100) {
          updateProgress(100);
          clearInterval(interval);
          // Complete the loading after reaching 100%
          setTimeout(() => {
            stopLoading();
          }, 500);
        } else {
          updateProgress(localProgress);
        }
      }
    }, 200);
    
    setProgressUpdateInterval(interval);
  };

  const handleStartFullPageLoader = () => {
    startLoading({
      message: "Processing your request...",
      variant: loaderVariant as any,
      animation: loaderAnimation as any,
      showProgressBar,
      showProgressInSpinner,
      blurBackground
    });
    
    if (useManualProgress) {
      simulateProgressUpdate();
    } else {
      // Automatically stop after 5 seconds if not using manual progress
      setTimeout(() => {
        stopLoading();
      }, 5000);
    }
  };

  const handleStartComponentLoader = () => {
    setIsComponentLoading(true);
    setComponentProgress(0);
    
    if (useManualProgress) {
      simulateProgressUpdate(true);
    } else {
      setTimeout(() => {
        setIsComponentLoading(false);
      }, 3000);
    }
  };

  return (
    <div className="container grid gap-8 py-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Dynamic Loader Components</h1>
        <p className="text-muted-foreground">
          Examples of different loading components with dynamic features.
        </p>
      </div>

      <Tabs defaultValue="loaders" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="loaders">Loader Examples</TabsTrigger>
          <TabsTrigger value="configuration">Configure Loaders</TabsTrigger>
        </TabsList>
        
        <TabsContent value="loaders" className="mt-6">
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Basic Loader</CardTitle>
                <CardDescription>Different sizes and variants</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col gap-4">
                <div className="flex flex-col gap-3">
                  <Loader size="sm" label="Small loader" variant={loaderVariant as any} animation={loaderAnimation as any} />
                  <Loader size="default" label="Default loader" variant={loaderVariant as any} animation={loaderAnimation as any} />
                  <Loader size="lg" label="Large loader" variant={loaderVariant as any} animation={loaderAnimation as any} />
                  <Loader size="xl" label="Extra large loader" variant={loaderVariant as any} animation={loaderAnimation as any} />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Progress Loaders</CardTitle>
                <CardDescription>Loaders with progress indicators</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col gap-4">
                <Loader 
                  size="lg" 
                  label="Downloading files..." 
                  variant={loaderVariant as any} 
                  animation={loaderAnimation as any}
                  progress={35} 
                />
                <Loader 
                  size="lg" 
                  label="Processing data..." 
                  variant={loaderVariant as any} 
                  animation={loaderAnimation as any}
                  progress={67} 
                />
                <Loader 
                  size="lg" 
                  label="Upload complete" 
                  variant={loaderVariant as any} 
                  animation={loaderAnimation as any}
                  progress={100} 
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Component Loading</CardTitle>
                <CardDescription>Loading state for a specific component</CardDescription>
              </CardHeader>
              <CardContent className="h-[200px] relative">
                {isComponentLoading ? (
                  <PageLoader 
                    label="Loading component..." 
                    variant={loaderVariant as any} 
                    animation={loaderAnimation as any}
                    progress={useManualProgress ? componentProgress : undefined}
                    showProgressBar={showProgressBar}
                    showProgressInSpinner={showProgressInSpinner}
                  />
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <p className="text-muted-foreground">Component content</p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button onClick={handleStartComponentLoader} className="w-full">
                  Simulate Component Loading
                </Button>
              </CardFooter>
            </Card>

            <Card className="sm:col-span-2 lg:col-span-3">
              <CardHeader>
                <CardTitle>Full Page Loading</CardTitle>
                <CardDescription>
                  Demo of the full page loading overlay with customizable options
                </CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center py-6">
                <Button onClick={handleStartFullPageLoader} size="lg">
                  Simulate Full Page Loading
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="configuration" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Loader Configuration</CardTitle>
              <CardDescription>
                Customize the appearance and behavior of the loaders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 sm:grid-cols-2">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="variant">Variant</Label>
                    <Select
                      value={loaderVariant}
                      onValueChange={setLoaderVariant}
                    >
                      <SelectTrigger id="variant">
                        <SelectValue placeholder="Select variant" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">Default</SelectItem>
                        <SelectItem value="primary">Primary</SelectItem>
                        <SelectItem value="secondary">Secondary</SelectItem>
                        <SelectItem value="destructive">Destructive</SelectItem>
                        <SelectItem value="accent">Accent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="animation">Animation</Label>
                    <Select
                      value={loaderAnimation}
                      onValueChange={setLoaderAnimation}
                    >
                      <SelectTrigger id="animation">
                        <SelectValue placeholder="Select animation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="spin">Spin</SelectItem>
                        <SelectItem value="pulse">Pulse</SelectItem>
                        <SelectItem value="bounce">Bounce</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="useManualProgress"
                      checked={useManualProgress}
                      onCheckedChange={setUseManualProgress}
                    />
                    <Label htmlFor="useManualProgress">Use Manual Progress</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="showProgressBar"
                      checked={showProgressBar}
                      onCheckedChange={setShowProgressBar}
                    />
                    <Label htmlFor="showProgressBar">Show Progress Bar</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="showProgressInSpinner"
                      checked={showProgressInSpinner}
                      onCheckedChange={setShowProgressInSpinner}
                    />
                    <Label htmlFor="showProgressInSpinner">Show Progress in Spinner</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="blurBackground"
                      checked={blurBackground}
                      onCheckedChange={setBlurBackground}
                    />
                    <Label htmlFor="blurBackground">Blur Background</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-center gap-4">
              <div className="flex items-center gap-2 p-4 border rounded-lg">
                <Loader 
                  variant={loaderVariant as any} 
                  animation={loaderAnimation as any} 
                  size="lg"
                  progress={useManualProgress ? 45 : undefined}
                  showProgress={showProgressInSpinner}
                  label="Preview"
                />
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 