"use client";

import { useState } from "react";
import Link from "next/link";
import {
  Globe,
  List,
  Briefcase,
  ShoppingBasket,
  Puzzle,
  Mail,
  Send,
  Share2,
  ThumbsDown,
  Link2,
  ShoppingBag,
  Wallet,
  BarChart3,
  LineChart,
  TrendingUp,
  Activity,
  Info,
  LucideIcon
} from "lucide-react";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AnimatedCounter } from "@/components/ui/animated-counter";
import { <PERSON><PERSON><PERSON> as <PERSON>hadc<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/line-chart";
import { Bar<PERSON><PERSON> as ShadcnBar<PERSON><PERSON> } from "@/components/ui/bar-chart";

// Stat Card component for reusability
interface StatCardProps {
  icon: LucideIcon;
  value: string | number;
  label: string;
  bgColor: string;
  iconColor: string;
  textColor: string;
  tooltip: string;
}

const StatCard = ({ 
  icon: Icon, 
  value, 
  label, 
  bgColor, 
  iconColor, 
  textColor, 
  tooltip 
}: StatCardProps) => (
  <div className={`${bgColor} rounded-lg p-4 shadow-sm border hover:shadow-md transition-all duration-200 group`}>
    <div className="flex items-center justify-between">
      <div className={`rounded-full ${iconColor} p-2 group-hover:scale-110 transition-transform duration-200`}>
        <Icon className={`h-5 w-5 ${textColor}`} />
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center cursor-help">
              <span className={`text-2xl font-bold ${textColor}`}>
                <AnimatedCounter value={value} />
              </span>
              <Info className="h-4 w-4 ml-1 text-muted-foreground opacity-70" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
    <div className={`mt-2 text-sm ${textColor.replace('600', '800').replace('400', '300')}`}>{label}</div>
  </div>
);

export default function DashboardPage() {
  const [earningsChartType, setEarningsChartType] = useState<"bar" | "line">("bar");
  const [performanceChartType, setPerformanceChartType] = useState<"bar" | "line">("bar");
  
  // Enhanced color palette for the application
  const colorPalette = {
    // Primary colors
    primary: {
      light: 'bg-indigo-50 dark:bg-indigo-900/20',
      medium: 'bg-indigo-100 dark:bg-indigo-800/30',
      border: 'border-indigo-200 dark:border-indigo-800/40',
      hover: 'hover:bg-indigo-100 dark:hover:bg-indigo-800/30',
      text: 'text-indigo-600 dark:text-indigo-400',
      focusBg: 'bg-indigo-500 dark:bg-indigo-600',
      focusText: 'text-white',
    },
    // Secondary / purple
    secondary: {
      light: 'bg-purple-50 dark:bg-purple-900/20',
      medium: 'bg-purple-100 dark:bg-purple-800/30',
      border: 'border-purple-200 dark:border-purple-800/40',
      hover: 'hover:bg-purple-100 dark:hover:bg-purple-800/30',
      text: 'text-purple-600 dark:text-purple-400',
      focusBg: 'bg-purple-500 dark:bg-purple-600',
      focusText: 'text-white',
    },
    // Success / green
    success: {
      light: 'bg-emerald-50 dark:bg-emerald-900/20',
      medium: 'bg-emerald-100 dark:bg-emerald-800/30',
      border: 'border-emerald-200 dark:border-emerald-800/40',
      hover: 'hover:bg-emerald-100 dark:hover:bg-emerald-800/30',
      text: 'text-emerald-600 dark:text-emerald-400',
      focusBg: 'bg-emerald-500 dark:bg-emerald-600',
      focusText: 'text-white',
    },
    // Info / blue
    info: {
      light: 'bg-sky-50 dark:bg-sky-900/20',
      medium: 'bg-sky-100 dark:bg-sky-800/30',
      border: 'border-sky-200 dark:border-sky-800/40',
      hover: 'hover:bg-sky-100 dark:hover:bg-sky-800/30',
      text: 'text-sky-600 dark:text-sky-400',
      focusBg: 'bg-sky-500 dark:bg-sky-600',
      focusText: 'text-white',
    },
    // Warning / amber
    warning: {
      light: 'bg-amber-50 dark:bg-amber-900/20',
      medium: 'bg-amber-100 dark:bg-amber-800/30',
      border: 'border-amber-200 dark:border-amber-800/40',
      hover: 'hover:bg-amber-100 dark:hover:bg-amber-800/30',
      text: 'text-amber-600 dark:text-amber-400',
      focusBg: 'bg-amber-500 dark:bg-amber-600',
      focusText: 'text-white',
    },
    // Danger / red
    danger: {
      light: 'bg-rose-50 dark:bg-rose-900/20',
      medium: 'bg-rose-100 dark:bg-rose-800/30',
      border: 'border-rose-200 dark:border-rose-800/40',
      hover: 'hover:bg-rose-100 dark:hover:bg-rose-800/30',
      text: 'text-rose-600 dark:text-rose-400',
      focusBg: 'bg-rose-500 dark:bg-rose-600',
      focusText: 'text-white',
    },
    // Neutral / slate
    neutral: {
      light: 'bg-slate-50 dark:bg-slate-900/20',
      medium: 'bg-slate-100 dark:bg-slate-800/30',
      border: 'border-slate-200 dark:border-slate-800/40',
      hover: 'hover:bg-slate-100 dark:hover:bg-slate-800/30',
      text: 'text-slate-600 dark:text-slate-400',
      focusBg: 'bg-slate-500 dark:bg-slate-600',
      focusText: 'text-white',
    },
    // Teal - for variety
    teal: {
      light: 'bg-teal-50 dark:bg-teal-900/20',
      medium: 'bg-teal-100 dark:bg-teal-800/30',
      border: 'border-teal-200 dark:border-teal-800/40',
      hover: 'hover:bg-teal-100 dark:hover:bg-teal-800/30',
      text: 'text-teal-600 dark:text-teal-400',
      focusBg: 'bg-teal-500 dark:bg-teal-600',
      focusText: 'text-white',
    },
    // Violet - for variety
    violet: {
      light: 'bg-violet-50 dark:bg-violet-900/20',
      medium: 'bg-violet-100 dark:bg-violet-800/30',
      border: 'border-violet-200 dark:border-violet-800/40',
      hover: 'hover:bg-violet-100 dark:hover:bg-violet-800/30',
      text: 'text-violet-600 dark:text-violet-400',
      focusBg: 'bg-violet-500 dark:bg-violet-600',
      focusText: 'text-white',
    },
    // Cyan - for variety
    cyan: {
      light: 'bg-cyan-50 dark:bg-cyan-900/20',
      medium: 'bg-cyan-100 dark:bg-cyan-800/30',
      border: 'border-cyan-200 dark:border-cyan-800/40',
      hover: 'hover:bg-cyan-100 dark:hover:bg-cyan-800/30',
      text: 'text-cyan-600 dark:text-cyan-400',
      focusBg: 'bg-cyan-500 dark:bg-cyan-600', 
      focusText: 'text-white',
    }
  };
  
  // Helper function to handle tooltips to avoid duplicate code and type issues
  const handleTooltipMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const tooltip = e.currentTarget.querySelector('.tooltip-value') as HTMLElement;
    if (tooltip) {
      tooltip.style.opacity = '1';
      tooltip.style.left = `${e.clientX - e.currentTarget.getBoundingClientRect().left}px`;
      tooltip.style.top = `${e.clientY - e.currentTarget.getBoundingClientRect().top - 30}px`;
    }
  };

  const handleTooltipLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    const tooltip = e.currentTarget.querySelector('.tooltip-value') as HTMLElement;
    if (tooltip) {
      tooltip.style.opacity = '0';
    }
  };

  // Daily earnings test data
  const dailyEarningsData = Array.from({ length: 31 }, (_, i) => ({
    day: `${i + 1}`,
    revenue: [1850, 2100, 1750, 1900, 2300, 2600, 2400, 2200, 1950, 1800, 2050, 2400, 2700, 2500, 2350, 2150, 2250, 2400, 2600, 2800, 2650, 2500, 2300, 2150, 2050, 2200, 2350, 2550, 2750, 2900, 3100][i],
  }));

  // Monthly earnings test data
  const monthlyEarningsData = [
    { month: 'Jan', revenue: 38000 },
    { month: 'Feb', revenue: 42000 },
    { month: 'Mar', revenue: 48775 },
    { month: 'Apr', revenue: 0 },
    { month: 'May', revenue: 0 },
    { month: 'Jun', revenue: 0 },
    { month: 'Jul', revenue: 0 },
    { month: 'Aug', revenue: 0 },
    { month: 'Sep', revenue: 0 },
    { month: 'Oct', revenue: 0 },
    { month: 'Nov', revenue: 0 },
    { month: 'Dec', revenue: 0 }
  ].filter(item => item.revenue > 0);

  // Performance chart test data
  const deliveryData = [
    { category: 'Delivered', value: 70 },
    { category: 'Soft Bounce', value: 15 },
    { category: 'Hard Bounce', value: 10 },
    { category: 'Blocked', value: 5 }
  ];

  const engagementData = [
    { category: 'Opens', value: 35 },
    { category: 'Clicks', value: 12 },
    { category: 'Leads', value: 8 },
    { category: 'Unsubs', value: 3 }
  ];
  
  return (
    <ContentLayout title="Dashboard">
      {/* Dashboard Content */}
      <div className="mt-6 space-y-6">
        <div className="grid grid-cols-12 gap-4 lg:gap-6">
          {/* Overview Section - Left Panel (4 Columns) */}
          <div className="col-span-12 lg:col-span-4 space-y-6">
            <Card className="hover:shadow-md transition-all duration-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl font-medium">
                  <Activity className="inline-block mr-2 h-5 w-5" />
                  Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  {/* Active Servers */}
                  <div className={`${colorPalette.info.light} rounded-lg p-4 shadow-sm ${colorPalette.info.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.info.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Globe className={`h-5 w-5 ${colorPalette.info.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.info.text}`}>
                        <AnimatedCounter value="24" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.info.text}`}>Active Servers</div>
                  </div>

                  {/* Active IPs */}
                  <div className={`${colorPalette.secondary.light} rounded-lg p-4 shadow-sm ${colorPalette.secondary.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.secondary.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <List className={`h-5 w-5 ${colorPalette.secondary.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.secondary.text}`}>
                        <AnimatedCounter value="186" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.secondary.text}`}>Active IPs</div>
                  </div>

                  {/* Affiliate Networks */}
                  <div className={`${colorPalette.success.light} rounded-lg p-4 shadow-sm ${colorPalette.success.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.success.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Briefcase className={`h-5 w-5 ${colorPalette.success.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.success.text}`}>
                        <AnimatedCounter value="12" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.success.text}`}>Affiliate Networks</div>
                  </div>

                  {/* Active Offers */}
                  <div className={`${colorPalette.teal.light} rounded-lg p-4 shadow-sm ${colorPalette.teal.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.teal.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <ShoppingBasket className={`h-5 w-5 ${colorPalette.teal.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.teal.text}`}>
                        <AnimatedCounter value="38" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.teal.text}`}>Active Offers</div>
                  </div>

                  {/* Daily Tests */}
                  <div className={`${colorPalette.primary.light} rounded-lg p-4 shadow-sm ${colorPalette.primary.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.primary.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Puzzle className={`h-5 w-5 ${colorPalette.primary.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.primary.text}`}>
                        <AnimatedCounter value="156" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.primary.text}`}>Daily Tests</div>
                  </div>

                  {/* Daily Drops */}
                  <div className={`${colorPalette.violet.light} rounded-lg p-4 shadow-sm ${colorPalette.violet.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.violet.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Mail className={`h-5 w-5 ${colorPalette.violet.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.violet.text}`}>
                        <AnimatedCounter value="32" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.violet.text}`}>Daily Drops</div>
                  </div>

                  {/* Daily Sent */}
                  <div className={`${colorPalette.teal.light} rounded-lg p-4 shadow-sm ${colorPalette.teal.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.teal.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Send className={`h-5 w-5 ${colorPalette.teal.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.teal.text}`}>
                        <AnimatedCounter value="1.2M" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.teal.text}`}>Daily Sent</div>
                  </div>

                  {/* Daily Delivered */}
                  <div className={`${colorPalette.success.light} rounded-lg p-4 shadow-sm ${colorPalette.success.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.success.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Share2 className={`h-5 w-5 ${colorPalette.success.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.success.text}`}>
                        <AnimatedCounter value="956K" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.success.text}`}>Daily Delivered</div>
                  </div>

                  {/* Daily Hard Bounced */}
                  <div className={`${colorPalette.danger.light} rounded-lg p-4 shadow-sm ${colorPalette.danger.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.danger.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <ThumbsDown className={`h-5 w-5 ${colorPalette.danger.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.danger.text}`}>
                        <AnimatedCounter value="244K" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.danger.text}`}>Daily Hard Bounced</div>
                  </div>

                  {/* Monthly Clicks */}
                  <div className={`${colorPalette.secondary.light} rounded-lg p-4 shadow-sm ${colorPalette.secondary.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.secondary.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Link2 className={`h-5 w-5 ${colorPalette.secondary.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.secondary.text}`}>
                        <AnimatedCounter value="98.6K" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.secondary.text}`}>Monthly Clicks</div>
                  </div>

                  {/* Monthly Leads */}
                  <div className={`${colorPalette.success.light} rounded-lg p-4 shadow-sm ${colorPalette.success.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.success.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <ShoppingBag className={`h-5 w-5 ${colorPalette.success.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.success.text}`}>
                        <AnimatedCounter value="12.4K" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.success.text}`}>Monthly Leads</div>
                  </div>

                  {/* Monthly Earnings */}
                  <div className={`${colorPalette.info.light} rounded-lg p-4 shadow-sm ${colorPalette.info.border} hover:shadow-md transition-all duration-200 group`}>
                    <div className="flex items-center justify-between">
                      <div className={`rounded-full ${colorPalette.info.medium} p-2 group-hover:scale-110 transition-transform duration-200`}>
                        <Wallet className={`h-5 w-5 ${colorPalette.info.text}`} />
                      </div>
                      <span className={`text-2xl font-bold ${colorPalette.info.text}`}>
                        <AnimatedCounter value="$48.5K" />
                      </span>
                    </div>
                    <div className={`mt-2 text-sm ${colorPalette.info.text}`}>Monthly Earnings</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Charts - Right Panel (8 Columns) */}
          <div className="col-span-12 lg:col-span-8 space-y-6">
            <Card className="hover:shadow-md transition-all duration-200">
              <CardHeader className="pb-2">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                  <CardTitle className="text-xl font-medium">
                    <BarChart3 className="inline-block mr-2 h-5 w-5" />
                    Performance Charts
                  </CardTitle>
                  <Tabs 
                    defaultValue="bar" 
                    value={performanceChartType} 
                    onValueChange={(value) => setPerformanceChartType(value as "bar" | "line")} 
                    className="w-[140px]"
                  >
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="bar">
                        <BarChart3 className="h-4 w-4 mr-1" />
                        Bar
                      </TabsTrigger>
                      <TabsTrigger value="line">
                        <LineChart className="h-4 w-4 mr-1" />
                        Line
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Chart 1: Delivery vs. Bounced */}
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <h3 className="text-sm font-medium mb-4">Delivery vs. Bounced (March 2025)</h3>
                    {performanceChartType === "bar" ? (
                      <div className="h-80 flex items-end space-x-2 sm:space-x-4">
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-green-500 h-[70%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-white font-medium text-[10px] sm:text-xs">70%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Delivered</div>
                        </div>
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-amber-400 h-[15%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-amber-800 font-medium text-[10px] sm:text-xs">15%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Soft Bounce</div>
                        </div>
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-red-500 h-[10%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-white font-medium text-[10px] sm:text-xs">10%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Hard Bounce</div>
                        </div>
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-gray-300 h-[5%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-gray-600 font-medium text-[10px] sm:text-xs">5%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Blocked</div>
                        </div>
                      </div>
                    ) : (
                      <ShadcnLineChart
                        data={deliveryData}
                        categories={["value"]}
                        index="category"
                        colors={["#22c55e", "#fbbf24", "#ef4444", "#9ca3af"]}
                        valueFormatter={(value) => `${value}%`}
                        yAxisWidth={48}
                        showLegend={false}
                      />
                    )}
                  </div>

                  {/* Chart 2: Opens/Clicks/Leads/Unsubs */}
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <h3 className="text-sm font-medium mb-4">Engagement (March 2025)</h3>
                    {performanceChartType === "bar" ? (
                      <div className="h-80 flex items-end space-x-2 sm:space-x-4">
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-blue-500 h-[35%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-white font-medium text-[10px] sm:text-xs">35%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Opens</div>
                        </div>
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-purple-500 h-[12%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-white font-medium text-[10px] sm:text-xs">12%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Clicks</div>
                        </div>
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-teal-500 h-[8%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-white font-medium text-[10px] sm:text-xs">8%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Leads</div>
                        </div>
                        <div className="relative h-full flex-1 flex flex-col justify-end group">
                          <div className="bg-red-400 h-[3%] rounded-t-sm group-hover:opacity-90 transition-opacity flex items-center justify-center">
                            <span className="text-white font-medium text-[10px] sm:text-xs">3%</span>
                          </div>
                          <div className="text-[10px] sm:text-xs font-medium mt-2 text-center">Unsubs</div>
                        </div>
                      </div>
                    ) : (
                      <ShadcnLineChart
                        data={engagementData}
                        categories={["value"]}
                        index="category"
                        colors={["#3b82f6", "#a855f7", "#14b8a6", "#f87171"]}
                        valueFormatter={(value) => `${value}%`}
                        yAxisWidth={48}
                        showLegend={false}
                      />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Status/Performance Card */}
            <Card className="hover:shadow-md transition-all duration-200">
              <CardHeader className="pb-2">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <CardTitle className="text-xl font-medium">
                    <Activity className="inline-block mr-2 h-5 w-5" />
                    Campaign Status
                  </CardTitle>
                  <Tabs defaultValue="today" className="w-full sm:w-[300px]">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="today">Today</TabsTrigger>
                      <TabsTrigger value="week">This Week</TabsTrigger>
                      <TabsTrigger value="month">This Month</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">Delivery Rate</p>
                      <span className="text-sm bg-green-100 text-green-800 py-0.5 px-2 rounded-full">Good</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="92.8%" />
                    </p>
                    <p className="text-xs text-green-600 mt-1">↑ 1.2% from yesterday</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">Open Rate</p>
                      <span className="text-sm bg-amber-100 text-amber-800 py-0.5 px-2 rounded-full">Average</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="34.6%" />
                    </p>
                    <p className="text-xs text-red-600 mt-1">↓ 0.3% from yesterday</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">CTR</p>
                      <span className="text-sm bg-green-100 text-green-800 py-0.5 px-2 rounded-full">Good</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="12.4%" />
                    </p>
                    <p className="text-xs text-green-600 mt-1">↑ 0.8% from yesterday</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">Conv. Rate</p>
                      <span className="text-sm bg-blue-100 text-blue-800 py-0.5 px-2 rounded-full">Excellent</span>
                    </div>
                    <p className="text-2xl font-bold mt-2">
                      <AnimatedCounter value="8.9%" />
                    </p>
                    <p className="text-xs text-green-600 mt-1">↑ 1.6% from yesterday</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Earnings Charts */}
        <div className="mt-8">
          <Card className="hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <CardTitle className="text-xl font-medium">
                  <LineChart className="inline-block mr-2 h-5 w-5" />
                  Earnings Analytics
                </CardTitle>
                <Tabs 
                  defaultValue="bar" 
                  value={earningsChartType} 
                  onValueChange={(value) => setEarningsChartType(value as "bar" | "line")} 
                  className="w-[140px]"
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="bar">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Bar
                    </TabsTrigger>
                    <TabsTrigger value="line">
                      <LineChart className="h-4 w-4 mr-1" />
                      Line
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Daily Earnings (March 2025) */}
                <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <h3 className="text-sm font-medium mb-4">Daily Earnings (March 2025)</h3>
                  {earningsChartType === "bar" ? (
                    <ShadcnBarChart
                      data={dailyEarningsData}
                      categories={["revenue"]}
                      index="day"
                      colors={["#84cc16"]}
                      valueFormatter={(value) => `$${value.toLocaleString()}`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  ) : (
                    <ShadcnLineChart
                      data={dailyEarningsData}
                      categories={["revenue"]}
                      index="day"
                      colors={["#84cc16"]}
                      valueFormatter={(value) => `$${value.toLocaleString()}`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  )}
                </div>

                {/* Monthly Earnings (2025) */}
                <div className="border rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <h3 className="text-sm font-medium mb-4">Monthly Earnings (2025)</h3>
                  {earningsChartType === "bar" ? (
                    <ShadcnBarChart
                      data={monthlyEarningsData}
                      categories={["revenue"]}
                      index="month"
                      colors={["#3b82f6"]}
                      valueFormatter={(value) => `$${(value / 1000).toFixed(1)}K`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  ) : (
                    <ShadcnLineChart
                      data={monthlyEarningsData}
                      categories={["revenue"]}
                      index="month"
                      colors={["#3b82f6"]}
                      valueFormatter={(value) => `$${(value / 1000).toFixed(1)}K`}
                      yAxisWidth={48}
                      showLegend={false}
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ContentLayout>
  );
} 