"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Search, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

interface SearchableSelectProps {
  value: string | string[]
  onValueChange: (value: string | string[]) => void
  placeholder?: string
  items: { 
    value: string; 
    label: string | React.ReactNode;
    searchableText?: string;
  }[]
  triggerClassName?: string
  id?: string
  searchPlaceholder?: string
  isMulti?: boolean
  badgeClassName?: string
  maxDisplayValues?: number
  hideSelectedInTrigger?: boolean
  hideValue?: boolean
}

export function SearchableSelect({
  value,
  onValueChange,
  placeholder,
  items,
  triggerClassName,
  id,
  searchPlaceholder = "Search...",
  isMulti = false,
  badgeClassName,
  maxDisplayValues = 3,
  hideSelectedInTrigger = false,
  hideValue = false
}: SearchableSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState("")
  const [internalValue, setInternalValue] = React.useState<string | string[]>(value)
  const inputRef = React.useRef<HTMLInputElement>(null)
  const searchContainerRef = React.useRef<HTMLDivElement>(null)
  const triggerRef = React.useRef<HTMLDivElement>(null)
  const dropdownRef = React.useRef<HTMLDivElement>(null)
  const [isUserTyping, setIsUserTyping] = React.useState(false)
  const typingTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)
  
  // Keep internal value in sync with external value
  React.useEffect(() => {
    setInternalValue(value)
  }, [value])

  // Handle clicks outside the component to close the dropdown
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        open && 
        triggerRef.current && 
        dropdownRef.current && 
        !triggerRef.current.contains(event.target as Node) && 
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setOpen(false)
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  // Ensure internal value is an array for multi-select mode
  const selectedValues = React.useMemo(() => {
    if (isMulti) {
      return Array.isArray(internalValue) ? internalValue : internalValue ? [internalValue] : []
    }
    return Array.isArray(internalValue) ? internalValue[0] || "" : internalValue || ""
  }, [internalValue, isMulti])

  // Check if a value is selected
  const isSelected = React.useCallback((itemValue: string) => {
    if (isMulti) {
      return Array.isArray(selectedValues) && selectedValues.includes(itemValue)
    }
    return selectedValues === itemValue
  }, [selectedValues, isMulti])

  // Get selected items for display
  const selectedItems = React.useMemo(() => {
    if (isMulti && Array.isArray(selectedValues)) {
      return items.filter(item => selectedValues.includes(item.value))
    } else {
      const selected = items.find(item => item.value === selectedValues)
      return selected ? [selected] : []
    }
  }, [items, selectedValues, isMulti])

  // Get the display for the trigger
  const getSelectedDisplay = React.useCallback(() => {
    // If hideValue is true, always show placeholder
    if (hideValue || !selectedItems.length) return placeholder
    
    // If hideSelectedInTrigger is true but not hideValue, show values only in multi-select mode
    if (hideSelectedInTrigger && !isMulti) return placeholder

    if (!isMulti) {
      // For single select, just show the actual value
      const item = selectedItems[0]
      if (typeof item.label === 'string') {
        return item.label
      }
      // Fallback for React node
      return item.value
    }

    // For multi-select, show count of selected items without brackets
    if (selectedItems.length > 0) {
      return `${selectedItems.length} Selected`;
    }

    return placeholder;
  }, [selectedItems, isMulti, placeholder, hideSelectedInTrigger, hideValue])
  
  // Filter items based on search query
  const filteredItems = React.useMemo(() => {
    if (!searchQuery) return items
    
    return items.filter((item) => {
      // First check if searchableText is available and matches
      if (item.searchableText) {
        return item.searchableText.toLowerCase().includes(searchQuery.toLowerCase())
      }
      
      // If the label is a string, search in it
      if (typeof item.label === 'string') {
        return item.label.toLowerCase().includes(searchQuery.toLowerCase())
      }
      
      // If the label is a React node, search in the value
      return item.value.toLowerCase().includes(searchQuery.toLowerCase())
    })
  }, [items, searchQuery])

  // Reset search when dropdown closes and focus input when it opens
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("")
      setIsUserTyping(false)
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    } else {
      // Use a more reliable approach to focus the input when dropdown opens
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus()
        }
      }, 50) // Small delay to ensure the dropdown is fully open
      
      return () => clearTimeout(timer)
    }
  }, [open])

  // Clean up any timeouts when component unmounts
  React.useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [])

  // Handle removing an item in multi-select mode
  const handleRemoveItem = (itemValue: string) => {
    if (!isMulti || !Array.isArray(selectedValues)) return
    
    const newValues = selectedValues.filter(val => val !== itemValue)
    setInternalValue(newValues)
    onValueChange(newValues)
  }

  // Handle search input changes with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation() // Prevent event from reaching Select component
    
    // Set the new search query
    setSearchQuery(e.target.value)
    
    // Indicate that user is typing to prevent selection
    setIsUserTyping(true)
    
    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }
    
    // Set a new timeout to indicate when typing has stopped
    typingTimeoutRef.current = setTimeout(() => {
      setIsUserTyping(false)
    }, 500)
  }

  // Handle manual selection of an item by clicking
  const handleItemClick = (itemValue: string, e: React.MouseEvent) => {
    // Prevent the default selection behavior
    e.preventDefault()
    e.stopPropagation()
    
    // If user is not typing, manually handle the selection
    if (!isUserTyping) {
      if (isMulti) {
        // For multi-select, toggle the selection
        let newValues: string[]
        
        if (Array.isArray(selectedValues)) {
          // Toggle the item
          if (selectedValues.includes(itemValue)) {
            newValues = selectedValues.filter(val => val !== itemValue)
          } else {
            newValues = [...selectedValues, itemValue]
          }
        } else {
          // Convert single value to array and add the new item
          newValues = selectedValues ? [selectedValues, itemValue] : [itemValue]
        }
        
        setInternalValue(newValues)
        onValueChange(newValues)
        // Don't close dropdown in multi-select mode
      } else {
        // For single select, just replace the value
        setInternalValue(itemValue)
        onValueChange(itemValue)
        setOpen(false)
      }
    }
  }

  // Stop click propagation for the search container
  const handleSearchContainerClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  // Handle mouse clicks on the input to prevent dropdown from closing
  const handleInputClick = (e: React.MouseEvent<HTMLInputElement>) => {
    e.stopPropagation()
    e.preventDefault()
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  // Prevent specific keys from closing the dropdown
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Block the event completely for these keys to prevent dropdown navigation
    if (
      e.key === "ArrowUp" || 
      e.key === "ArrowDown" || 
      e.key === "Enter" ||
      e.key === "Tab" ||
      (e.key === " " && !inputRef.current?.value)  // Prevent space from triggering selection when input is empty
    ) {
      e.preventDefault()
      e.stopPropagation()
      return
    }
    
    // Handle Escape key specially
    if (e.key === "Escape") {
      if (searchQuery) {
        // If there's search text, just clear it
        e.preventDefault()
        e.stopPropagation()
        setSearchQuery("")
      } else {
        // Otherwise close the dropdown
        setOpen(false)
      }
      return
    }

    // For any other key press, set the typing state
    setIsUserTyping(true)
    
    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }
    
    // Set a new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsUserTyping(false)
    }, 500)
  }

  // Handle clearing all selected values
  const handleClearAll = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isMulti) {
      setInternalValue([])
      onValueChange([])
    } else {
      setInternalValue("")
      onValueChange("")
    }
  }

  // Close the dropdown with a small delay to ensure click events are processed
  const closeDropdown = () => {
    setTimeout(() => {
      setOpen(false)
    }, 100);
  }

  return (
    <div className="relative w-full">
      <div 
        ref={triggerRef}
        onClick={() => setOpen(!open)}
        className={cn(
          "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", 
          triggerClassName,
          isMulti && "min-h-[2.5rem] h-auto"
        )}
      >
        <div className={cn("flex-grow truncate", 
          (isMulti && Array.isArray(selectedValues) && selectedValues.length === 0) || 
          (!isMulti && !selectedValues) || hideValue || (hideSelectedInTrigger && !isMulti) ? "text-muted-foreground" : ""
        )}>
          {getSelectedDisplay()}
        </div>
        <div className="flex items-center gap-1 ml-2 flex-shrink-0">
          {!hideValue && ((isMulti && Array.isArray(selectedValues) && selectedValues.length > 0) || 
             (!isMulti && selectedValues && !hideSelectedInTrigger)) && (
            <button
              type="button"
              onClick={handleClearAll}
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Clear</span>
            </button>
          )}
          <ChevronsUpDown className="h-4 w-4 opacity-50" />
        </div>
      </div>
      
      {open && (
        <div 
          ref={dropdownRef}
          className="absolute z-50 min-w-[8rem] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 mt-1"
        >
          <div 
            ref={searchContainerRef}
            className="px-2 pt-2 pb-1"
            onClick={handleSearchContainerClick}
          >
            <div className="flex items-center border rounded-md px-2">
              <Search className="h-4 w-4 text-muted-foreground mr-2" />
              <Input
                ref={inputRef}
                value={searchQuery}
                onChange={handleSearchChange}
                onKeyDown={handleInputKeyDown}
                onClick={handleInputClick}
                placeholder={searchPlaceholder}
                className="h-8 border-none focus-visible:ring-0 focus-visible:ring-offset-0 px-0"
                autoComplete="off"
                // Additional events to capture and prevent propagation
                onFocus={(e) => e.stopPropagation()}
                onBlur={(e) => e.stopPropagation()}
                onMouseDown={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                }}
              />
            </div>
          </div>
          
          <div className="max-h-[200px] overflow-y-auto p-1">
            {filteredItems.length > 0 ? (
              filteredItems.map((item) => (
                <div 
                  key={item.value}
                  className={cn(
                    "relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                    isSelected(item.value) && "bg-accent text-accent-foreground"
                  )}
                  onClick={(e) => handleItemClick(item.value, e)}
                  role="option"
                  aria-selected={isSelected(item.value)}
                >
                  <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                    {isMulti ? (
                      <Checkbox 
                        checked={isSelected(item.value)} 
                        className="h-4 w-4"
                        onCheckedChange={() => {}} // Handled by the parent div onClick
                      />
                    ) : (
                      isSelected(item.value) && <Check className="h-4 w-4" />
                    )}
                  </span>
                  {typeof item.label === 'string' ? (
                    <span>{item.label}</span>
                  ) : (
                    item.label
                  )}
                </div>
              ))
            ) : (
              <div className="py-2 text-center text-sm text-muted-foreground">
                No results found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
} 