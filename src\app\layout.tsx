import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "geist/font/sans";

import "./globals.css";

import { ThemeProvider } from "@/components/providers/theme-provider";
import { AlertProvider } from "@/components/providers/alert-provider";
import { LoadingProvider } from "@/components/providers/loading-provider";
import { RouteProgress } from "@/components/route-progress";

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.APP_URL
      ? `${process.env.APP_URL}`
      : process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : `http://localhost:${process.env.PORT || 3000}`
  ),
  title: "SendFlux | Powerful Emailing Platform",
  description:
    "SendFlux is a powerful emailing platform with advanced features and a clean, modern interface.",
  alternates: {
    canonical: "/"
  },
  openGraph: {
    url: "/",
    title: "SendFlux | Powerful Emailing Platform",
    description:
    "SendFlux is a powerful emailing platform with advanced features and a clean, modern interface.",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "SendFlux | Powerful Emailing Platform",
    description:
      "SendFlux is a powerful emailing platform with advanced features and a clean, modern interface."
  }
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/sendflux-icon.svg" type="image/svg+xml" />
        <link rel="shortcut icon" href="/sendflux-icon.svg" />
        {/* Script to clean up bis_skin_checked attributes added by browser extensions */}
        <script dangerouslySetInnerHTML={{
          __html: `
            (function() {
              if (typeof window !== 'undefined') {
                const observer = new MutationObserver((mutations) => {
                  for (const mutation of mutations) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'bis_skin_checked') {
                      mutation.target.removeAttribute('bis_skin_checked');
                    }
                  }
                });
                
                // Start observing when DOM is ready
                window.addEventListener('DOMContentLoaded', () => {
                  observer.observe(document.body, {
                    attributes: true,
                    subtree: true,
                    attributeFilter: ['bis_skin_checked']
                  });
                  
                  // Also clean any existing attributes
                  document.querySelectorAll('[bis_skin_checked]').forEach(el => {
                    el.removeAttribute('bis_skin_checked');
                  });
                });
              }
            })();
          `
        }} />
      </head>
      <body className={GeistSans.className} suppressHydrationWarning>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <AlertProvider>
            <LoadingProvider>
              <RouteProgress color="hsl(var(--primary))" height={3} />
              {children}
            </LoadingProvider>
          </AlertProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
