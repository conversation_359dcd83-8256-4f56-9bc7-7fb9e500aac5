import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";

// Simple tabs implementation - could be replaced with a more robust component later
interface TabProps {
  title: string;
  isActive: boolean;
  onClick: () => void;
}

const Tab = ({ title, isActive, onClick }: TabProps) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "px-4 py-2 font-medium text-sm rounded-md transition-colors",
        isActive 
          ? "bg-primary text-primary-foreground" 
          : "hover:bg-muted text-muted-foreground hover:text-foreground"
      )}
    >
      {title}
    </button>
  );
};

// Custom styled tag component for consistent appearance and copy functionality
const TagItem = ({ tag, description }: { tag: string; description: string }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(tag)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        console.error('Failed to copy: ', err);
      });
  };

  return (
    <li className="flex items-start gap-2 py-0.5">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div 
              className="flex items-center gap-1.5 group cursor-pointer" 
              onClick={copyToClipboard}
            >
              <code className="px-1.5 py-0.5 bg-muted rounded text-sm font-medium border border-transparent group-hover:border-primary/20 transition-colors">
                {tag}
              </code>
              <span className="text-xs text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity">
                {copied ? (
                  <Check className="h-3.5 w-3.5 text-green-500" />
                ) : (
                  <Copy className="h-3.5 w-3.5" />
                )}
              </span>
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="text-xs">
            {copied ? 'Copied!' : 'Click to copy'}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <span className="text-muted-foreground">{description}</span>
    </li>
  );
};

export function HelpButton() {
  const [activeTab, setActiveTab] = useState("header-tags");

  const tabContent = {
    "header-tags": (
      <div className="grid grid-cols-3 gap-6 py-4 text-sm">
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-bold text-lg">Main Tags</h3>
            <ul className="space-y-1 pl-0 list-none">
              <TagItem tag="[ip]" description="The Current IP" />
              <TagItem tag="[rdns]" description="The Current IP's RDNS" />
              <TagItem tag="[ptr]" description="The Remote IP's PTR" />
              <TagItem tag="[domain]" description="The Current IP's Domain" />
              <TagItem tag="[custom_domain]" description="The Custom Vmta's Domain" />
              <TagItem tag="[static_domain]" description="The Static Domain" />
              <TagItem tag="[smtp_user]" description="Smtp Username (Smtp Process Only)" />
              <TagItem tag="[server]" description="Server Name" />
              <TagItem tag="[email_id]" description="Destinator Email Id" />
              <TagItem tag="[email]" description="Destinator Email" />
              <TagItem tag="[first_name]" description="Destinator First Name" />
              <TagItem tag="[last_name]" description="Destinator Last Name" />
              <TagItem tag="[return_path]" description="Return Path" />
              <TagItem tag="[from_name]" description="From Name" />
              <TagItem tag="[subject]" description="Subject" />
              <TagItem tag="[mail_date]" description="Current Date" />
              <TagItem tag="[message_id]" description="Generated Message ID" />
              <TagItem tag="[negative]" description="Negative file content (if any)" />
              <TagItem tag="[placeholder1 .... n]" description="The Current placeholder from 1 to n value" />
              <TagItem tag="[auto_reply_mailbox]" description="Auto Reply Mailbox (if any)" />
            </ul>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-bold text-lg">Unique Random Tags</h3>
            <p className="text-muted-foreground">(eg: [uan_12] for fixed size and [uan_5_15] for random ranged size)</p>
            <ul className="space-y-1 pl-0 list-none">
              <TagItem tag="[ua]" description="Unique Alpha Random" />
              <TagItem tag="[ual]" description="Unique Alpha Lowercase Random" />
              <TagItem tag="[uau]" description="Unique Alpha Uppercase Random" />
              <TagItem tag="[uan]" description="Unique Alphanumeric Random" />
              <TagItem tag="[uanl]" description="Unique Alphanumeric Lowercase Random" />
              <TagItem tag="[uanu]" description="Unique Alphanumeric Uppercase Random" />
              <TagItem tag="[un]" description="Unique Numeric Random" />
              <TagItem tag="[uhu]" description="Unique Uppercase Hex Random" />
              <TagItem tag="[uhl]" description="Unique Lowercase Hex Random" />
            </ul>
          </div>
          
          <div className="space-y-2 mt-6">
            <h3 className="font-bold text-lg">Random Tags</h3>
            <p className="text-muted-foreground">(eg: [an_12] for fixed size and [an_5_15] for random ranged size)</p>
            <ul className="space-y-1 pl-0 list-none">
              <TagItem tag="[a]" description="Alpha Random" />
              <TagItem tag="[al]" description="Alpha Lowercase Random" />
              <TagItem tag="[au]" description="Alpha Uppercase Random" />
              <TagItem tag="[an]" description="Alphanumeric Random" />
              <TagItem tag="[anl]" description="Alphanumeric Lowercase Random" />
              <TagItem tag="[anu]" description="Alphanumeric Uppercase Random" />
              <TagItem tag="[n]" description="Numeric Random" />
              <TagItem tag="[hu]" description="Uppercase Hex Random" />
              <TagItem tag="[hl]" description="Lowercase Hex Random" />
            </ul>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-bold text-lg">Links Tags</h3>
            <ul className="space-y-1 pl-0 list-none">
              <TagItem tag="[open]" description="Open Link, eg: http://[domain]/[open]" />
              <TagItem tag="[url]" description="Click Link, eg: http://[domain]/[url]" />
              <TagItem tag="[unsub]" description="Unsub Link, eg: http://[domain]/[unsub]" />
              <TagItem tag="[optout]" description="OptOut Link, eg: http://[domain]/[optout]" />
            </ul>
          </div>
          
          <div className="space-y-2 mt-6">
            <h3 className="font-bold text-lg">Short Links Tags</h3>
            <p className="text-muted-foreground">(No need for domain tag and no need for http://)</p>
            <ul className="space-y-1 pl-0 list-none">
              <TagItem tag="[short_open]" description="Open Link, eg: [short_open]" />
              <TagItem tag="[short_url]" description="Click Link, eg: [short_url]" />
              <TagItem tag="[short_unsub]" description="Unsub Link, eg: [short_unsub]" />
              <TagItem tag="[short_optout]" description="OptOut Link, eg: [short_optout]" />
            </ul>
          </div>
          
          <div className="space-y-2 mt-6">
            <h3 className="font-bold text-lg">VMTAS Types</h3>
            
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold">Default Vmta:</h4>
                <p className="text-muted-foreground text-xs">Sends with default vmtas</p>
                <ul className="space-y-1 pl-0 list-none mt-1">
                  <TagItem tag="[rdns]" description="will have default rdns" />
                  <TagItem tag="[domain]" description="will have default domain" />
                  <TagItem tag="[custom_domain]" description="will be empty" />
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold">Custom Vmta:</h4>
                <p className="text-muted-foreground text-xs">Sends with custom created vmtas (IF ANY)</p>
                <ul className="space-y-1 pl-0 list-none mt-1">
                  <TagItem tag="[rdns]" description="will have custom domain" />
                  <TagItem tag="[domain]" description="will have custom domain" />
                  <TagItem tag="[custom_domain]" description="will have custom domain" />
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold">Merged Vmta:</h4>
                <p className="text-muted-foreground text-xs">Sends with custom created vmtas (IF ANY)</p>
                <ul className="space-y-1 pl-0 list-none mt-1">
                  <TagItem tag="[rdns]" description="will have default rdns" />
                  <TagItem tag="[domain]" description="will have default domain" />
                  <TagItem tag="[custom_domain]" description="will have custom domain" />
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
    "settings": (
      <div className="py-4 text-sm">
        <p className="text-muted-foreground mb-4">This tab will contain settings-related help information.</p>
        <div className="p-8 flex items-center justify-center border rounded-md border-dashed">
          <span className="text-muted-foreground">Settings documentation coming soon</span>
        </div>
      </div>
    ),
    "templates": (
      <div className="py-4 text-sm">
        <p className="text-muted-foreground mb-4">This tab will contain templates-related help information.</p>
        <div className="p-8 flex items-center justify-center border rounded-md border-dashed">
          <span className="text-muted-foreground">Templates documentation coming soon</span>
        </div>
      </div>
    )
  };

  return (
    <Dialog>
      <TooltipProvider disableHoverableContent>
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button 
                className="rounded-full w-8 h-8 bg-background mr-2"
                variant="outline" 
                size="icon"
              >
                <HelpCircle className="h-4 w-4" />
                <span className="sr-only">Help</span>
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent side="bottom">Help</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Documentation</DialogTitle>
        </DialogHeader>
        
        {/* Tabs Navigation */}
        <div className="flex gap-2 border-b pb-2">
          <Tab 
            title="Header Tags" 
            isActive={activeTab === "header-tags"} 
            onClick={() => setActiveTab("header-tags")} 
          />
          <Tab 
            title="Settings" 
            isActive={activeTab === "settings"} 
            onClick={() => setActiveTab("settings")} 
          />
          <Tab 
            title="Templates" 
            isActive={activeTab === "templates"} 
            onClick={() => setActiveTab("templates")} 
          />
        </div>
        
        {/* Tab Content */}
        <div className="mt-4">
          {tabContent[activeTab as keyof typeof tabContent]}
        </div>
      </DialogContent>
    </Dialog>
  );
} 