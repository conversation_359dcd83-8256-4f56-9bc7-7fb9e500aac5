"use client";

import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { produce } from "immer";
import { useEffect, useState } from "react";

type UISettings = { 
  sidebar: {
    disabled: boolean;
    isHoverOpen: boolean;
    submenuHoverDelay: number;
    useNavbar: boolean;
  };
  // Add other UI-related settings here as needed
};

type UISettingsStore = {
  settings: UISettings;
  setSettings: (settings: Partial<UISettings>) => void;
  setSidebarSettings: (sidebarSettings: Partial<UISettings['sidebar']>) => void;
};

const initialState: Pick<UISettingsStore, 'settings'> = {
  settings: { 
    sidebar: {
      disabled: false,
      isHoverOpen: false,
      submenuHoverDelay: 300,
      useNavbar: false
    }
  }
};

export const useUISettings = create(
  persist<UISettingsStore>(
    (set, get) => ({
      ...initialState,
      setSettings: (settings: Partial<UISettings>) => {
        set(
          produce((state: UISettingsStore) => {
            state.settings = { ...state.settings, ...settings };
          })
        );
      },
      setSidebarSettings: (sidebarSettings: Partial<UISettings['sidebar']>) => {
        set(
          produce((state: UISettingsStore) => {
            state.settings.sidebar = { ...state.settings.sidebar, ...sidebarSettings };
          })
        );
      }
    }),
    {
      name: "ui-settings",
      storage: createJSONStorage(() => {
        return typeof window !== 'undefined' ? window.localStorage : undefined as any;
      }),
      skipHydration: true
    }
  )
);

// This is a React hook that helps with hydration
export function useHydratedUISettings() {
  const [hydrated, setHydrated] = useState(false);
  
  useEffect(() => {
    // This effect only runs on the client, so it's safe to access localStorage
    useUISettings.persist.rehydrate();
    setHydrated(true);
  }, []);
  
  return { hydrated };
} 