import Link from "next/link";

import MtaTestsMonitor from "@/components/admin-panel/mta-tests-monitor";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  B<PERSON><PERSON><PERSON>bPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";

export default function MtaTestsMonitorPage() {
  return (
    <ContentLayout title="MTA Tests Monitor">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/production">Production</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>MTA Tests Monitor</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <MtaTestsMonitor />
    </ContentLayout>
  );
} 