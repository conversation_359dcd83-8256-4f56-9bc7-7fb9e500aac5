"use client";

import Link from "next/link";
import { useState, useC<PERSON>back } from "react";
import { ChevronDown, ChevronUp, MoreVertical, Globe, Server, Shield, AlertTriangle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";

// Add a comprehensive list of countries with ISO codes and names
const countries = [
  { code: 'us', name: 'United States' }, { code: 'gb', name: 'United Kingdom' }, { code: 'ca', name: 'Canada' },
  { code: 'au', name: 'Australia' }, { code: 'de', name: 'Germany' }, { code: 'fr', name: 'France' },
  { code: 'es', name: 'Spain' }, { code: 'it', name: 'Italy' }, { code: 'nl', name: 'Netherlands' },
  { code: 'cn', name: 'China' }
];

const getCountryName = (code: string): string => {
  const country = countries.find(c => c.code === code);
  return country ? country.name : code.toUpperCase();
};

export default function IspsPage() {
  // Data for ISPs
  interface IspItem {
    id: number;
    name: string;
    geo: string;
    domains: string[];
    status: string;
  }

  const [ispItems, setIspItems] = useState<IspItem[]>([
    { id: 1, name: 'Gmail', geo: 'us', domains: ['gmail.com', 'googlemail.com'], status: 'active' },
    { id: 2, name: 'Hotmail/Outlook', geo: 'us', domains: ['hotmail.com', 'outlook.com', 'live.com'], status: 'active' },
    { id: 3, name: 'Yahoo Mail', geo: 'us', domains: ['yahoo.com', 'ymail.com', 'rocketmail.com'], status: 'active' },
    { id: 4, name: 'GMX', geo: 'de', domains: ['gmx.de', 'gmx.net', 'gmx.com'], status: 'active' },
    { id: 5, name: 'Web.de', geo: 'de', domains: ['web.de'], status: 'active' },
    { id: 6, name: 'Orange', geo: 'fr', domains: ['orange.fr', 'wanadoo.fr'], status: 'active' },
    { id: 7, name: 'Free', geo: 'fr', domains: ['free.fr'], status: 'active' },
    { id: 8, name: 'Libero', geo: 'it', domains: ['libero.it'], status: 'active' },
    { id: 9, name: 'Virgilio', geo: 'it', domains: ['virgilio.it'], status: 'paused' },
    { id: 10, name: 'KPN', geo: 'nl', domains: ['kpnmail.nl', 'planet.nl'], status: 'active' },
    { id: 11, name: 'Ziggo', geo: 'nl', domains: ['ziggo.nl', 'chello.nl'], status: 'active' },
    { id: 12, name: '163.com', geo: 'cn', domains: ['163.com', '126.com'], status: 'active' },
    { id: 13, name: 'QQ Mail', geo: 'cn', domains: ['qq.com', 'foxmail.com'], status: 'active' },
    { id: 14, name: 'Telefonica', geo: 'es', domains: ['telefonica.net', 'terra.es'], status: 'active' },
    { id: 15, name: 'BT Internet', geo: 'gb', domains: ['btinternet.com', 'btopenworld.com'], status: 'paused' },
  ]);

  // Sorting functionality
  const [sortConfig, setSortConfig] = useState<{
    key: keyof IspItem | null;
    direction: 'asc' | 'desc' | null;
  }>({
    key: null,
    direction: null
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter states
  const [geoFilter, setGeoFilter] = useState<string[] | null>(null);
  const [statusFilter, setStatusFilter] = useState<string[] | null>(null);

  // Reset all filters
  const resetFilters = () => {
    setGeoFilter(null);
    setStatusFilter(null);
    setCurrentPage(1);
  };

  // Helper function for multi-select changes
  const handleMultiSelectChange = (setter: (value: string[] | null) => void) => (value: string | string[]) => {
    const arrayValue = Array.isArray(value) ? value : [value];
    setter(arrayValue.length > 0 ? arrayValue : null);
    setCurrentPage(1); // Reset to page 1 when filters change
  };

  // Get unique geos from data
  const getUniqueGeos = () => {
    const geos = [...new Set(ispItems.map(item => item.geo))];
    return geos.map(geo => ({
      value: geo,
      label: (
        <div className="flex items-center gap-2">
          <img
            src={`https://flagcdn.com/w20/${geo.toLowerCase()}.png`}
            className="h-4 w-6 object-cover"
            alt={getCountryName(geo)}
          />
          <span>{getCountryName(geo)}</span>
        </div>
      )
    }));
  };

  // Get unique statuses from data
  const getUniqueStatuses = () => {
    const statuses = [...new Set(ispItems.map(item => item.status))];
    return statuses.map(status => ({
      value: status,
      label: (
        <div className="flex items-center gap-2">
          {status === 'active' ? (
            <Shield className="h-4 w-4 text-green-500" />
          ) : (
            <AlertTriangle className="h-4 w-4 text-amber-500" />
          )}
          <span className="capitalize">{status}</span>
        </div>
      )
    }));
  };

  // Get filtered data
  const getFilteredData = () => {
    return ispItems.filter(item => {
      const geoMatch = !geoFilter || geoFilter.includes(item.geo);
      const statusMatch = !statusFilter || statusFilter.includes(item.status);

      return geoMatch && statusMatch;
    });
  };

  // Sorting function for data items
  const sortData = useCallback((key: keyof IspItem) => {
    let direction: 'asc' | 'desc' | null = 'asc';

    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        direction = 'desc';
      } else if (sortConfig.direction === 'desc') {
        direction = null;
      }
    }

    setSortConfig({ key, direction });
  }, [sortConfig]);

  // Get sorted and paginated data
  const getSortedData = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    let sortedItems = [...getFilteredData()];

    if (sortConfig.key && sortConfig.direction) {
      sortedItems.sort((a, b) => {
        if (a[sortConfig.key!] < b[sortConfig.key!]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key!] > b[sortConfig.key!]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return sortedItems.slice(startIndex, endIndex);
  }, [ispItems, sortConfig, currentPage, geoFilter, statusFilter, itemsPerPage]);

  // Function to get sorting icon for headers
  const getSortIcon = useCallback((key: keyof IspItem) => {
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        return <ChevronUp className="h-3 w-3 text-primary" />;
      } else if (sortConfig.direction === 'desc') {
        return <ChevronDown className="h-3 w-3 text-primary" />;
      }
    }
    return (
      <div className="flex flex-col ml-1">
        <ChevronUp className="h-3 w-3" />
        <ChevronDown className="h-3 w-3 -mt-1" />
      </div>
    );
  }, [sortConfig]);

  // Pagination functions
  const filteredItems = getFilteredData();
  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Country flag component
  const CountryFlag = ({ country }: { country: string }) => {
    return (
      <div className="flex items-center justify-center">
        <img
          src={`https://flagcdn.com/w20/${country.toLowerCase()}.png`}
          className="h-4 w-6 object-cover"
          alt={getCountryName(country)}
          title={getCountryName(country)}
        />
      </div>
    );
  };

  return (
    <ContentLayout title="ISPs">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/data-manager">Data Manager</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>ISPs</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* ISPs Card Table */}
      <Card className="mt-4">
        <CardHeader className="flex flex-row items-center justify-between py-2 px-4">
          <CardTitle className="text-sm font-medium">ISPs</CardTitle>

          <div className="flex items-center gap-2">
            {/* Filters moved to right side */}
            <div className="flex items-center gap-2">
              <Label htmlFor="geo-filter" className="font-medium text-xs whitespace-nowrap">GEO:</Label>
              <SearchableSelect
                id="geo-filter"
                value={geoFilter || []}
                onValueChange={handleMultiSelectChange(setGeoFilter)}
                placeholder="All GEOs"
                triggerClassName="w-[100px] h-6 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700 text-xs"
                searchPlaceholder="Search GEOs..."
                isMulti={true}
                hideSelectedInTrigger={false}
                items={getUniqueGeos()}
              />
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="status-filter" className="font-medium text-xs whitespace-nowrap">STATUS:</Label>
              <SearchableSelect
                id="status-filter"
                value={statusFilter || []}
                onValueChange={handleMultiSelectChange(setStatusFilter)}
                placeholder="All Statuses"
                triggerClassName="w-[100px] h-6 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700 text-xs"
                searchPlaceholder="Search statuses..."
                isMulti={true}
                hideSelectedInTrigger={false}
                items={getUniqueStatuses()}
              />
            </div>

            <Button
              variant="outline"
              size="icon"
              onClick={resetFilters}
              className="h-6 w-6 bg-white dark:bg-gray-950 border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-900 transition-all"
              disabled={
                (!geoFilter || geoFilter.length === 0) &&
                (!statusFilter || statusFilter.length === 0)
              }
              title={
                (!geoFilter || geoFilter.length === 0) &&
                (!statusFilter || statusFilter.length === 0)
                ? "No active filters"
                : "Reset Filters"
              }
            >
              <RefreshCw className="h-2.5 w-2.5" />
            </Button>

            <div className="h-4 w-px bg-gray-300 dark:bg-gray-600 mx-1"></div>

            <Button
              size="sm"
              className="h-6 text-xs bg-green-600 hover:bg-green-700 text-white px-2"
            >
              Import ISPs
            </Button>
            <Button
              size="sm"
              className="h-6 text-xs bg-purple-600 hover:bg-purple-700 text-white px-2"
            >
              Add New ISP
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="rounded-md">
            <div className="overflow-auto border rounded-md">
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="hover:bg-muted/50">
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('name')}>
                      <div className="flex items-center">
                        NAME
                        {getSortIcon('name')}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('geo')}>
                      <div className="flex items-center">
                        GEO
                        {getSortIcon('geo')}
                      </div>
                    </TableHead>
                    <TableHead className="font-medium text-muted-foreground">
                      <div className="flex items-center">
                        DOMAINS
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData('status')}>
                      <div className="flex items-center">
                        STATUS
                        {getSortIcon('status')}
                      </div>
                    </TableHead>
                    <TableHead className="font-medium text-muted-foreground">
                      <div className="flex items-center">
                        ACTION
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getSortedData().map((item) => (
                    <TableRow key={item.id} className="border-b transition-colors hover:bg-muted/50">
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Server className="h-4 w-4 text-blue-500" />
                          {item.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <CountryFlag country={item.geo} />
                          <span className="text-sm text-muted-foreground">{getCountryName(item.geo)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {item.domains.map((domain, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <Globe className="h-3 w-3 mr-1 text-green-500" />
                              {domain}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                          {item.status === 'active' ? (
                            <div className="flex items-center gap-1">
                              <Shield className="h-3 w-3 text-green-500" />
                              Active
                            </div>
                          ) : (
                            <div className="flex items-center gap-1">
                              <AlertTriangle className="h-3 w-3 text-amber-500" />
                              Paused
                            </div>
                          )}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>Edit</DropdownMenuItem>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Configure</DropdownMenuItem>
                            <DropdownMenuItem className="text-red-500">Delete</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            {/* Pagination */}
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground flex items-center">
                <span>
                  Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, filteredItems.length)} of {filteredItems.length} entries
                </span>
                <div className="flex items-center ml-4">
                  <span className="mr-2 text-xs">Show:</span>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => {
                      setItemsPerPage(Number(value));
                      setCurrentPage(1); // Reset to page 1
                    }}
                  >
                    <SelectTrigger className="h-7 w-16 text-xs border-gray-200">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="15">15</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                  className="h-8 px-3"
                >
                  Previous
                </Button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant="outline"
                    size="sm"
                    onClick={() => goToPage(page)}
                    className={`h-8 w-8 p-0 ${
                      currentPage === page
                        ? 'bg-purple-100 dark:bg-purple-900 border-purple-200 dark:border-purple-800 text-purple-700 dark:text-purple-300'
                        : ''
                    }`}
                  >
                    {page}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                  className="h-8 px-3"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </ContentLayout>
  );
}
