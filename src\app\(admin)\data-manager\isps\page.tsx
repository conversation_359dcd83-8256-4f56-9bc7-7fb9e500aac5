"use client";

import Link from "next/link";
import { use<PERSON><PERSON>back, useState } from "react";
import { MoreVertical, Globe2, Building, ChevronUp, ChevronDown } from "lucide-react";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

// Country helpers (lightweight list for flags)
const countries = [
  { code: "us", name: "United States" },
  { code: "gb", name: "United Kingdom" },
  { code: "ca", name: "Canada" },
  { code: "au", name: "Australia" },
  { code: "de", name: "Germany" },
  { code: "fr", name: "France" },
  { code: "es", name: "Spain" },
  { code: "it", name: "Italy" },
  { code: "nl", name: "Netherlands" },
  { code: "cn", name: "China" },
  { code: "ru", name: "Russia" },
  { code: "in", name: "India" },
];

const getCountryName = (code: string) => countries.find((c) => c.code === code)?.name ?? code.toUpperCase();

export default function IspsPage() {
  interface IspItem {
    id: number;
    name: string;
    geo: string; // primary country/region
    domains: string[]; // known login/mail domains
    domainsCount: number; // cached for sorting
    status: "active" | "limited" | "blocked";
  }

  const [isps, setIsps] = useState<IspItem[]>([
    { id: 1, name: "Gmail", geo: "us", domains: ["gmail.com", "googlemail.com"], domainsCount: 2, status: "active" },
    { id: 2, name: "Yahoo Mail", geo: "us", domains: ["yahoo.com", "ymail.com", "rocketmail.com"], domainsCount: 3, status: "active" },
    { id: 3, name: "Outlook", geo: "us", domains: ["outlook.com", "hotmail.com", "live.com"], domainsCount: 3, status: "active" },
    { id: 4, name: "AOL", geo: "us", domains: ["aol.com"], domainsCount: 1, status: "limited" },
    { id: 5, name: "GMX", geo: "de", domains: ["gmx.de", "gmx.net"], domainsCount: 2, status: "active" },
    { id: 6, name: "Proton Mail", geo: "ch", domains: ["proton.me", "protonmail.com"], domainsCount: 2, status: "active" },
    { id: 7, name: "iCloud Mail", geo: "us", domains: ["icloud.com", "me.com", "mac.com"], domainsCount: 3, status: "active" },
    { id: 8, name: "Yandex", geo: "ru", domains: ["yandex.ru", "yandex.com"], domainsCount: 2, status: "active" },
    { id: 9, name: "Zoho Mail", geo: "in", domains: ["zoho.com", "zohomail.com"], domainsCount: 2, status: "active" },
    { id: 10, name: "163 Mail", geo: "cn", domains: ["163.com"], domainsCount: 1, status: "active" },
    { id: 11, name: "126 Mail", geo: "cn", domains: ["126.com"], domainsCount: 1, status: "active" },
    { id: 12, name: "Libero", geo: "it", domains: ["libero.it"], domainsCount: 1, status: "limited" },
  ]);

  // Sorting
  const [sortConfig, setSortConfig] = useState<{ key: keyof IspItem | null; direction: "asc" | "desc" | null }>({ key: null, direction: null });
  const sortData = useCallback(
    (key: keyof IspItem) => {
      let direction: "asc" | "desc" | null = "asc";
      if (sortConfig.key === key) {
        if (sortConfig.direction === "asc") direction = "desc";
        else if (sortConfig.direction === "desc") direction = null;
      }
      setSortConfig({ key, direction });
    },
    [sortConfig]
  );

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(isps.length / itemsPerPage);
  const goToPreviousPage = () => currentPage > 1 && setCurrentPage(currentPage - 1);
  const goToNextPage = () => currentPage < totalPages && setCurrentPage(currentPage + 1);

  const getSortedData = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    let items = [...isps];
    if (sortConfig.key && sortConfig.direction) {
      items.sort((a, b) => {
        const av = a[sortConfig.key!];
        const bv = b[sortConfig.key!];
        if (av < bv) return sortConfig.direction === "asc" ? -1 : 1;
        if (av > bv) return sortConfig.direction === "asc" ? 1 : -1;
        return 0;
      });
    }
    return items.slice(startIndex, endIndex);
  }, [isps, sortConfig, currentPage]);

  const getSortIcon = useCallback(
    (key: keyof IspItem) => {
      if (sortConfig.key === key) {
        if (sortConfig.direction === "asc") return <ChevronUp className="h-3 w-3 text-primary" />;
        if (sortConfig.direction === "desc") return <ChevronDown className="h-3 w-3 text-primary" />;
      }
      return (
        <div className="flex flex-col ml-1">
          <ChevronUp className="h-3 w-3" />
          <ChevronDown className="h-3 w-3 -mt-1" />
        </div>
      );
    },
    [sortConfig]
  );

  const CountryFlag = ({ country }: { country: string }) => (
    <div className="flex items-center justify-center">
      <img
        src={`https://flagcdn.com/w20/${country.toLowerCase()}.png`}
        className="h-4 w-6 object-cover"
        alt={getCountryName(country)}
        title={getCountryName(country)}
      />
    </div>
  );

  return (
    <ContentLayout title="ISPs">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/data-manager">Data Manager</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>ISPs</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Card className="mt-4">
        <CardHeader className="flex flex-row items-center justify-between py-3 px-5">
          <CardTitle className="text-base font-medium">ISPs</CardTitle>
          <div className="flex space-x-2">
            <Button size="sm" className="shadow h-8 rounded-md px-3 text-xs bg-green-600 hover:bg-green-700 text-white">Import</Button>
            <Button size="sm" className="shadow h-8 rounded-md px-3 text-xs bg-purple-600 hover:bg-purple-700 text-white">Add ISP</Button>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="rounded-md">
            <div className="overflow-auto border rounded-md">
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="hover:bg-muted/50">
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData("name")}>
                      <div className="flex items-center">NAME{getSortIcon("name")}</div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData("geo")}>
                      <div className="flex items-center">GEO{getSortIcon("geo")}</div>
                    </TableHead>
                    <TableHead className="font-medium text-muted-foreground">
                      <div className="flex items-center">DOMAINS</div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData("domainsCount")}>
                      <div className="flex items-center">
                        <Globe2 className="h-3 w-3 mr-1 text-indigo-500" /> COUNT{getSortIcon("domainsCount")}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer font-medium text-muted-foreground" onClick={() => sortData("status")}>
                      <div className="flex items-center">STATUS{getSortIcon("status")}</div>
                    </TableHead>
                    <TableHead className="font-medium text-muted-foreground">
                      <div className="flex items-center">ACTION</div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getSortedData().map((item) => (
                    <TableRow key={item.id} className="border-b transition-colors hover:bg-muted/50">
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          <span>{item.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <CountryFlag country={item.geo} />
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {item.domains.slice(0, 3).map((d) => (
                            <span key={d} className="px-2 py-0.5 rounded bg-muted text-xs">{d}</span>
                          ))}
                          {item.domains.length > 3 && (
                            <span className="px-2 py-0.5 rounded bg-muted text-xs">+{item.domains.length - 3} more</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Globe2 className="h-3 w-3 mr-1 text-indigo-500" />
                          {item.domainsCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={item.status === "active" ? "default" : item.status === "limited" ? "secondary" : "destructive"}>
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>Edit</DropdownMenuItem>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Export</DropdownMenuItem>
                            <DropdownMenuItem className="text-red-500">Delete</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, isps.length)} to {Math.min(currentPage * itemsPerPage, isps.length)} of {isps.length} ISPs
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={goToPreviousPage} disabled={currentPage === 1}>
                  Previous
                </Button>
                <Button variant="outline" size="sm" onClick={goToNextPage} disabled={currentPage === totalPages}>
                  Next
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </ContentLayout>
  );
}
