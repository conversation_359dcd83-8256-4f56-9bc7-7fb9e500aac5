"use client";

import Link from "next/link";
import { ChevronR<PERSON>, Ellipsis } from "lucide-react";
import { usePathname } from "next/navigation";

import { cn } from "@/lib/utils";
import { getMenuList } from "@/lib/menu-list";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CollapseMenuButton } from "@/components/admin-panel/collapse-menu-button";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider
} from "@/components/ui/tooltip";

interface MenuProps {
  isOpen: boolean | undefined;
  horizontal?: boolean;
}

export function Menu({ isOpen, horizontal = false }: MenuProps) {
  const pathname = usePathname();
  const menuList = getMenuList(pathname);

  return (
    <ScrollArea className={cn(
      "flex h-full [&_[data-radix-scroll-area-scrollbar]]:hidden",
      horizontal ? "flex-row w-full overflow-x-auto" : "flex-col"
    )}>
      <nav className={cn(
        "flex h-full",
        horizontal ? "flex-row" : "flex-col"
      )}>
        <ul className={cn(
          "flex h-full",
          horizontal ? "flex-row items-center space-x-1" : "flex-col",
          isOpen === false && !horizontal ? "items-center" : ""
        )}>
          {menuList.map(({ groupLabel, menus }, groupIndex) => (
            <li key={groupIndex} className={cn(
              "w-full",
              horizontal ? "flex-shrink-0" : "",
              groupLabel && !horizontal ? "pt-5" : "",
              isOpen === false && !horizontal ? "flex flex-col items-center" : "",
              horizontal && groupIndex > 0 ? "ml-6" : ""
            )}>
              {!horizontal && ((isOpen && groupLabel) || isOpen === undefined) ? (
                <p className="text-sm font-medium text-muted-foreground px-4 pb-2 max-w-[248px] truncate">
                  {groupLabel}
                </p>
              ) : !horizontal && !isOpen && isOpen !== undefined && groupLabel ? (
                <TooltipProvider>
                  <Tooltip delayDuration={100}>
                    <TooltipTrigger className="w-full">
                      <div className="w-full flex justify-center items-center">
                        <div className="h-[1px] w-8 bg-border my-2 rounded-full"></div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{groupLabel}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                !horizontal && <p className="pb-2"></p>
              )}
              <div className={cn(
                horizontal ? "flex flex-row space-x-1" : ""
              )}>
                {menus.map(({ href, label, icon: Icon, active, submenus }, menuIndex) => (
                  !submenus || submenus.length === 0 || horizontal ? (
                    <div className={cn(
                      horizontal ? "flex-shrink-0" : "w-full",
                      !horizontal && isOpen === false ? "flex justify-center" : ""
                    )} key={menuIndex}>
                      <TooltipProvider disableHoverableContent>
                        <Tooltip delayDuration={100}>
                          <TooltipTrigger asChild>
                            <Button
                              variant={
                                (active === undefined && pathname.startsWith(href)) ||
                                active
                                  ? "secondary"
                                  : "ghost"
                              }
                              className={cn(
                                "relative mb-1.5",
                                horizontal ? "h-8 px-3" : "",
                                !horizontal && isOpen === false ? 
                                  "w-9 h-9 rounded-full justify-center p-0 hover:bg-secondary hover:shadow-sm transition-colors duration-200" : 
                                  "w-full h-9 justify-start"
                              )}
                              asChild
                            >
                              <Link href={href}>
                                <span className={cn(
                                  "flex items-center justify-center",
                                  horizontal ? "mr-2" : "",
                                  !horizontal && isOpen === false ? "w-6 h-6" : horizontal ? "" : "w-6 h-6 mr-2"
                                )}>
                                  <Icon size={horizontal ? 16 : (isOpen === false ? 20 : 18)} 
                                        className={isOpen === false && !horizontal ? "text-primary" : ""} />
                                </span>
                                <p
                                  className={cn(
                                    "truncate",
                                    horizontal ? "text-sm" : "",
                                    !horizontal && isOpen === false
                                      ? "opacity-0 w-0 ml-0 absolute"
                                      : "opacity-100 w-auto max-w-[220px]"
                                  )}
                                >
                                  {label}
                                </p>
                              </Link>
                            </Button>
                          </TooltipTrigger>
                          {isOpen === false && !horizontal && (
                            <TooltipContent side="right">{label}</TooltipContent>
                          )}
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  ) : !horizontal ? (
                    <div className={cn(
                      "w-full",
                      isOpen === false ? "flex justify-center" : ""
                    )} key={menuIndex}>
                      <CollapseMenuButton
                        icon={Icon}
                        label={label}
                        active={
                          active === undefined ? pathname.startsWith(href) : active
                        }
                        submenus={submenus}
                        isOpen={isOpen}
                      />
                    </div>
                  ) : null
                ))}
              </div>
            </li>
          ))}
        </ul>
      </nav>
    </ScrollArea>
  );
}