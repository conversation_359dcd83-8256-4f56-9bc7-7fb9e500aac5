"use client";

import Link from "next/link";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DatePicker } from "@/components/ui/date-picker";

export default function AddSmtpPage() {
  const [bulkSmtpText, setBulkSmtpText] = useState("");
  const [expireDate, setExpireDate] = useState<Date>();
  const [status, setStatus] = useState("active");
  const [provider, setProvider] = useState("");
  
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const text = await file.text();
      setBulkSmtpText(text);
    }
  };

  const handleBulkSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Split the text into lines and validate each line
    const lines = bulkSmtpText.split('\n').filter(line => line.trim());
    const smtpConfigs = lines.map(line => {
      const [host, port, login, pass] = line.split('|');
      return { host, port, login, pass };
    });
    
    // TODO: Handle the SMTP configurations (API call or state management)
    console.log('SMTP Configs:', smtpConfigs);
  };

  const handleExpireDateSelect = (date: Date | undefined) => {
    setExpireDate(date);
  };

  return (
    <ContentLayout title="Add New Smtp Server">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/deliverability/smtp">Deliverability</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/deliverability/smtp">SMTP</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Add New SMTP Server</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="grid gap-6 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>SMTP Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="custom" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="custom">Custom SMTP</TabsTrigger>
                <TabsTrigger value="bulk">Bulk SMTP</TabsTrigger>
              </TabsList>
              <TabsContent value="custom">
                <form className="space-y-6">
                  <div className="grid gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="host">SMTP Host</Label>
                        <Input
                          id="host"
                          placeholder="smtp.example.com"
                          className="w-full"
                        />
                      </div>
                      
                      <div className="grid gap-2">
                        <Label htmlFor="port">Port</Label>
                        <Input id="port" placeholder="587" className="w-full" />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="username">Username</Label>
                        <Input
                          id="username"
                          placeholder="smtp_username"
                          className="w-full"
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="password">Password</Label>
                        <Input
                          id="password"
                          type="password"
                          placeholder="••••••••"
                          className="w-full"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="encryption">Encryption</Label>
                        <Select defaultValue="tls">
                          <SelectTrigger id="encryption">
                            <SelectValue placeholder="Select encryption" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="tls">TLS</SelectItem>
                            <SelectItem value="ssl">SSL</SelectItem>
                            <SelectItem value="none">None</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="provider">Provider</Label>
                        <Select defaultValue={provider} onValueChange={setProvider}>
                          <SelectTrigger id="provider">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gmail">Gmail</SelectItem>
                            <SelectItem value="outlook">Outlook</SelectItem>
                            <SelectItem value="yahoo">Yahoo</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="expire-date">Expire Date</Label>
                        <DatePicker
                          date={expireDate}
                          onSelect={handleExpireDateSelect}
                        />
                      </div>
                      
                      <div className="grid gap-2">
                        <Label htmlFor="status">Status</Label>
                        <Select defaultValue={status} onValueChange={setStatus}>
                          <SelectTrigger id="status">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <Button variant="outline" asChild>
                      <Link href="/deliverability/smtp">Cancel</Link>
                    </Button>
                    <Button type="submit">Save SMTP</Button>
                  </div>
                </form>
              </TabsContent>
              <TabsContent value="bulk">
                <form className="space-y-6" onSubmit={handleBulkSubmit}>
                  <div className="grid grid-cols-2 gap-8">
                    {/* Left side - Textarea */}
                    <div className="space-y-4">
                      <div className="grid gap-2">
                        <Label htmlFor="bulk-smtp">Enter SMTPs</Label>
                        <Textarea
                          id="bulk-smtp"
                          value={bulkSmtpText}
                          onChange={(e) => setBulkSmtpText(e.target.value)}
                          placeholder="host|port|login|password"
                          className="min-h-[250px] font-mono"
                        />
                      </div>
                    </div>

                    {/* Right side - Import */}
                    <div className="space-y-4">
                      <div className="grid gap-2">
                        <Label>Import SMTPs List</Label>
                        <div className="border-2 border-dashed border-gray-200 rounded-lg p-8 text-center">
                          <Input
                            type="file"
                            accept=".txt"
                            onChange={handleFileChange}
                            className="hidden"
                            id="file-upload"
                          />
                          <Label 
                            htmlFor="file-upload" 
                            className="cursor-pointer inline-flex flex-col items-center justify-center"
                          >
                            <svg
                              className="w-12 h-12 text-gray-400 mb-3"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                              />
                            </svg>
                            <span className="text-base font-medium">
                              Click to upload or drag and drop
                            </span>
                            <span className="text-sm text-gray-500">
                              .txt files only
                            </span>
                          </Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4 mt-8">
                    <Button variant="outline" asChild>
                      <Link href="/deliverability/smtp">Cancel</Link>
                    </Button>
                    <Button type="submit">Save SMTPs</Button>
                  </div>
                </form>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </ContentLayout>
  );
} 