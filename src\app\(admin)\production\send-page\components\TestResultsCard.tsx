import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";

interface TestResultItem {
  id: number;
  server: string;
  interface: string;
  returnPath: string;
  receivedEmail: string;
  testStatus: 'Success' | 'Failed' | 'Pending';
}

const TestResultsCard: React.FC = () => {
  // Sample test data
  const testResults: TestResultItem[] = [
    {
      id: 1,
      server: '2xx.xx.xx.19',
      interface: '192.168.1.1',
      returnPath: 'bounce.mx1.tx1.BannerDnsSrvPlatform-01.lightbase.ru',
      receivedEmail: '<EMAIL>',
      testStatus: 'Success',
    },
    {
      id: 2,
      server: '2xx.xx.xx.37',
      interface: '192.168.1.2',
      returnPath: 'bounce.mx1.tx1.BannerDnsSrvPlatform-01.lightbase.ru',
      receivedEmail: '<EMAIL>',
      testStatus: 'Success',
    },
  ];

  const totalTests = 2;
  const completedTests = 3;

  return (
    <Card className="w-full shadow-md hover:shadow-lg transition-shadow duration-200 mt-6">
      <CardHeader 
        className="flex flex-row items-center justify-between py-3 pl-4 relative"
      >
        <div className="flex items-center gap-2 flex-grow">
          <CardTitle>Test Results</CardTitle>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-sm font-medium text-muted-foreground">
            {completedTests} of {totalTests} tests completed
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Progress indicator */}
        <div className="mb-5">
          <div className="w-full bg-secondary rounded-full h-2.5">
            <div 
              className="h-2.5 rounded-full"
              style={{ 
                width: `${Math.min(100, (completedTests / totalTests) * 100)}%`,
                backgroundColor: completedTests > totalTests ? 'var(--amber-500, #f59e0b)' : 'var(--blue-600, #2563eb)'
              }}
            ></div>
          </div>
        </div>

        {/* Test results table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  SERVER
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  IP/INTERFACE
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  RETURN PATH
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  RECEIVED EMAIL
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  TEST STATUS
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {testResults.map((result) => (
                <tr key={result.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {result.server}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {result.interface}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground max-w-[200px] truncate">
                    {result.returnPath}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                    {result.receivedEmail}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      result.testStatus === 'Success' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                        : result.testStatus === 'Failed'
                        ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                    }`}>
                      {result.testStatus}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination info */}
        <div className="text-sm text-muted-foreground mt-4">
          Showing 1 to {testResults.length} of {testResults.length} entries
        </div>
      </CardContent>
    </Card>
  );
};

export default TestResultsCard; 